#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import schedule
import time
import datetime
import pytz
import logging
import sys
import multiprocessing

# 引入主逻辑脚本
try:
    from chat_test import test, models  # 确保 main.py 在相同目录或 PYTHONPATH 中
except ImportError as e:
    print("无法导入 main.py 中的 test 或 models。请确保文件存在并包含这两个对象。")
    print(f"导入错误: {e}")
    sys.exit(1)

# 设置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_test_log.txt'),
        logging.StreamHandler()
    ]
)

# 设置时区为中国时间
china_tz = pytz.timezone('Asia/Shanghai')

# 子进程封装，添加超时保护
def run_with_timeout(llm, key, url, timeout_sec=300):
    def wrapper(queue):
        try:
            test(llm, key, url)
            queue.put("success")
        except Exception as e:
            queue.put(f"error: {e}")

    q = multiprocessing.Queue()
    p = multiprocessing.Process(target=wrapper, args=(q,))
    p.start()
    p.join(timeout=timeout_sec)

    if p.is_alive():
        p.terminate()
        p.join()
        return f"超时（>{timeout_sec}秒）终止"
    else:
        result = q.get()
        return result

def job():
    now = datetime.datetime.now(china_tz)
    logging.info("\n========== 执行定时测试 ==========")
    logging.info(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')} (中国时间)")

    for llm in models:
        logging.info(f"开始测试模型: {llm}")
        try:
            result = run_with_timeout(llm, models[llm]['key'], models[llm]['url'])
            if result == "success":
                logging.info(f"完成模型测试: {llm}\n")
            else:
                logging.warning(f"模型 {llm} 测试异常: {result}")
        except Exception as e:
            logging.error(f"模型 {llm} 测试过程捕获异常: {e}")

# 一天内完成测试，设置每小时运行一次
schedule.every().hour.do(job)

# 可选：立即运行一次以验证
job()

print("定时测试脚本启动，每小时执行一次，按 Ctrl+C 停止")

while True:
    schedule.run_pending()
    time.sleep(1)
