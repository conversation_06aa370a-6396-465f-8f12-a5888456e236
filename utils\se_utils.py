# -*- coding: utf-8 -*-
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
#from selenium.webdriver.firefox.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException

# Set up Firefox options
#options = Options()
#options.add_argument('headless')
# options.page_load_strategy = 'eager'

# Initialize WebDriver
#driver = webdriver.Firefox(options=options)
#driver = webdriver.Chrome(options=options)
#driver.implicitly_wait(10)

def search_from_internet(keyword):

    service = Service(executable_path="D:\\chromedriver-win64\\chromedriver.exe")
    options = Options()
    options.add_argument('headless')
    driver = webdriver.Chrome(options=options, service=service)
    driver.implicitly_wait(10)

    results = {}

    # Navigate to the URL
    driver.get("https://www.baidu.com")

    print("get")

    # Find elements
    text = driver.find_element(By.ID, "kw")
    btn = driver.find_element(By.ID, "su")

    if text is not None and btn is not None:
        # Format the search query
        # kw = f"{province} {year} {indicator} {key}"
        kw = "2020年河北省针对财产险保费收入有什么政策"
        text.send_keys(keyword)
        btn.click()

        try:
            print("wait for the results to load")
            # Wait for the results to load
            WebDriverWait(driver, 5).until(EC.presence_of_element_located((By.CSS_SELECTOR, ".result.c-container.xpath-log.new-pmd>.c-container .c-title>a")))
        except TimeoutException:
            # Handle Timeout
            driver.quit()
            exit()

        print("look for elements")
        # Find elements containing the results
        elements = driver.find_elements(By.CSS_SELECTOR, ".result.c-container.xpath-log.new-pmd>.c-container")
        for element in elements:
            try:
                print("parse element")
                # Extract title and link
                title = element.find_element(By.CSS_SELECTOR, ".c-title>a")
                link = title.get_attribute("href")
                title_text = title.text
                abs = element.find_element(By.CSS_SELECTOR, ".content-right_8Zs40")

                # if "..." in title_text:
                #     driver2 = webdriver.Firefox(options=options)
                #     driver2.get(link)
                #     # Optionally wait for the page to load
                #     title_text = driver2.title
                #     driver2.quit()

                # Store result
                result = {
                    "indicator": "财产险保费",
                    "province": "河北省",
                    "year": "2020",
                    "title": title_text,
                    "url": link,
                    "abs": abs.text
                }
                results[link] = result
            except (NoSuchElementException, TimeoutException, WebDriverException):
                continue

    # Close the driver
    driver.quit()
    print(results)
    return results

if __name__ == "__main__":
    print(search_from_internet("123"))