# -*- coding: utf-8 -*-
"""
手动下载模型脚本
"""
import os
from huggingface_hub import snapshot_download
from transformers import AutoTokenizer, AutoModel
from langchain_community.embeddings import HuggingFaceBgeEmbeddings

def download_embedding_model():
    """下载 BGE embedding 模型"""
    print("开始下载 BGE embedding 模型...")
    try:
        # 方法1：使用 HuggingFaceBgeEmbeddings 自动下载
        embeddings = HuggingFaceBgeEmbeddings(
            model_name="BAAI/bge-large-zh",
            model_kwargs={'device': 'cpu'},  # 先用CPU避免CUDA问题
            query_instruction='为这个句子生成表示以用于检索相关文章：'
        )
        print("BGE embedding 模型下载完成！")
        return True
    except Exception as e:
        print(f"BGE embedding 模型下载失败: {e}")
        return False

def download_llm_model():
    """下载 Qwen2.5 LLM 模型"""
    print("开始下载 Qwen2.5 LLM 模型...")
    try:
        # 下载 tokenizer
        tokenizer = AutoTokenizer.from_pretrained(
            "Qwen/Qwen2.5-7B-Instruct", 
            trust_remote_code=True
        )
        print("Tokenizer 下载完成！")
        
        # 下载模型（注意：这会下载很大的文件，约15GB）
        model = AutoModel.from_pretrained(
            "Qwen/Qwen2.5-7B-Instruct", 
            trust_remote_code=True,
            torch_dtype="auto",  # 自动选择数据类型
            device_map="auto"    # 自动选择设备
        )
        print("Qwen2.5 LLM 模型下载完成！")
        return True
    except Exception as e:
        print(f"Qwen2.5 LLM 模型下载失败: {e}")
        return False

def download_with_snapshot():
    """使用 snapshot_download 批量下载"""
    print("使用 snapshot_download 下载模型...")
    
    # 下载 BGE 模型
    try:
        bge_path = snapshot_download(
            repo_id="BAAI/bge-large-zh",
            cache_dir="./models",  # 指定本地缓存目录
            resume_download=True   # 支持断点续传
        )
        print(f"BGE 模型下载到: {bge_path}")
    except Exception as e:
        print(f"BGE 模型下载失败: {e}")
    
    # 下载 Qwen 模型
    try:
        qwen_path = snapshot_download(
            repo_id="Qwen/Qwen2.5-7B-Instruct",
            cache_dir="./models",  # 指定本地缓存目录
            resume_download=True   # 支持断点续传
        )
        print(f"Qwen 模型下载到: {qwen_path}")
    except Exception as e:
        print(f"Qwen 模型下载失败: {e}")

def check_model_cache():
    """检查已下载的模型"""
    import platform
    
    if platform.system() == "Windows":
        cache_dir = os.path.expanduser("~/.cache/huggingface/hub")
    else:
        cache_dir = os.path.expanduser("~/.cache/huggingface/hub")
    
    print(f"HuggingFace 缓存目录: {cache_dir}")
    
    if os.path.exists(cache_dir):
        models = [d for d in os.listdir(cache_dir) if d.startswith("models--")]
        print(f"已下载的模型: {len(models)} 个")
        for model in models:
            print(f"  - {model}")
    else:
        print("缓存目录不存在，尚未下载任何模型")

if __name__ == "__main__":
    print("=== 模型下载工具 ===")
    print("1. 检查已下载模型")
    print("2. 下载 BGE embedding 模型")
    print("3. 下载 Qwen2.5 LLM 模型")
    print("4. 使用 snapshot_download 批量下载")
    print("5. 全部下载")
    
    choice = input("请选择操作 (1-5): ").strip()
    
    if choice == "1":
        check_model_cache()
    elif choice == "2":
        download_embedding_model()
    elif choice == "3":
        download_llm_model()
    elif choice == "4":
        download_with_snapshot()
    elif choice == "5":
        print("开始下载所有模型...")
        download_embedding_model()
        download_llm_model()
    else:
        print("无效选择")
    
    print("\n最终检查:")
    check_model_cache()
