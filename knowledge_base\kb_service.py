# -*- coding: utf-8 -*-
"""
FAISS知识库
TODO：
1. 实现于本地持久化中间件的联动，数据库/文件系统？
2. 抽象统一接口，便于扩展向量数据库的实现
"""
import os
import shutil

from knowledge_base.kb_base import KBService
# from server.knowledge_base.kb_cache.faiss_cache import kb_faiss_pool, ThreadSafeFaiss
from langchain_community.vectorstores import FAISS
from utils.knowledge_base_utils import KnowledgeFile, get_kb_path, get_vs_path, torch_gc
from utils.embedding_utils import EmbeddingsFunAdapter
from langchain.docstore.document import Document
from typing import List, Dict


class FaissKBService(KBService):
    vs_path: str
    kb_path: str
    vector_name: str = None
    vector_store: FAISS = None

    def vs_type(self) -> str:
        return "FAISS"

    def get_vs_path(self):
        return get_vs_path(self.kb_name, self.vector_name)

    def get_kb_path(self):
        return get_kb_path(self.kb_name)

    def load_vector_store(self) -> FAISS:
        return self.vector_store

    def save_vector_store(self):
        self.vector_store.save_local(self.vs_path)

    def get_doc_by_ids(self, ids: List[str]) -> List[Document]:
        return [self.vector_store.docstore._dict.get(id) for id in ids]

    def do_init(self):
        self.vector_name = self.vector_name or self.embed_model
        self.kb_path = self.get_kb_path()
        self.vs_path = self.get_vs_path()

        # 如果向量数据库已经存在，直接加载本地数据库
        if os.path.isfile(os.path.join(self.vs_path, "index.faiss")):
            embeddings = EmbeddingsFunAdapter(self.embed_model)
            local_vector_store = FAISS.load_local(self.vs_path, embeddings, normalize_L2=True, allow_dangerous_deserialization=True)
            self.vector_store = local_vector_store
        else:
            # 如果不存在，创建新的向量数据库
            if not os.path.exists(self.vs_path):
                os.makedirs(self.vs_path)
            # 创建
            embeddings = EmbeddingsFunAdapter(self.embed_model)
            doc = Document(page_content="init", metadata={})
            new_vector_store = FAISS.from_documents([doc], embeddings, normalize_L2=True)
            ids = list(new_vector_store.docstore._dict.keys())
            new_vector_store.delete(ids)
            # 保存
            new_vector_store.save_local(self.vs_path)
            self.vector_store = new_vector_store


    def do_create_kb(self):
        if not os.path.exists(self.vs_path):
            os.makedirs(self.vs_path)
        self.load_vector_store()

    def do_drop_kb(self):
        self.clear_vs()
        try:
            shutil.rmtree(self.kb_path)
        except Exception:
            ...

    def do_search(self,
                  query: str,
                  top_k: int,
                  score_threshold: float = 1,
                  ) -> List[Document]:
        embed_func = EmbeddingsFunAdapter(self.embed_model)
        embeddings = embed_func.embed_query(query)
        docs = self.vector_store.similarity_search_with_score_by_vector(embeddings, k=top_k, score_threshold=score_threshold)
        return docs

    def do_add_doc(self,
                   docs: List[Document],
                   **kwargs,
                   ) -> List[Dict]:
        data = self._docs_to_embeddings(docs) # 将向量化单独出来可以减少向量库的锁定时间
        ids = self.vector_store.add_embeddings(text_embeddings=zip(data["texts"], data["embeddings"]),
                                               metadatas=data["metadatas"])
        if not kwargs.get("not_refresh_vs_cache"):
            self.vector_store.save_local(self.vs_path)
        doc_infos = [{"id": id, "metadata": doc.metadata} for id, doc in zip(ids, docs)]
        torch_gc()
        return doc_infos

    def do_delete_doc(self,
                      kb_file: KnowledgeFile,
                      **kwargs):
        ids = [k for k, v in self.vector_store.docstore._dict.items() if v.metadata.get("source") == kb_file.filename]
        if len(ids) > 0:
            self.vector_store.delete(ids)
        if not kwargs.get("not_refresh_vs_cache"):
            self.vector_store.save_local(self.vs_path)
        return ids

    def do_clear_vs(self):
        try:
            shutil.rmtree(self.vs_path)
        except Exception:
            ...
        os.makedirs(self.vs_path, exist_ok=True)

    def exist_doc(self, file_name: str):
        if super().exist_doc(file_name):
            return "in_db"

        content_path = os.path.join(self.kb_path, "content")
        if os.path.isfile(os.path.join(content_path, file_name)):
            return "in_folder"
        else:
            return False


if __name__ == '__main__':
    faissService = FaissKBService("test")
    faissService.add_doc(KnowledgeFile("README.md", "test"))
    faissService.delete_doc(KnowledgeFile("README.md", "test"))
    faissService.do_drop_kb()
    print(faissService.search_docs("如何启动api服务"))