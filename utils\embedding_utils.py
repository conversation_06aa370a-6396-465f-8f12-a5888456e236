# -*- coding: utf-8 -*-
"""
embedding函数工具类
"""
from langchain.docstore.document import Document
from langchain.embeddings.base import Embeddings
from langchain_community.embeddings import HuggingFaceBgeEmbeddings
import numpy as np
from configs.local_model_config import EMBEDDING_MODEL
from typing import List, Dict


def normalize(embeddings: List[List[float]]) -> np.ndarray:
    '''
    sklearn.preprocessing.normalize 的替代（使用 L2），避免安装 scipy, scikit-learn
    '''
    norm = np.linalg.norm(embeddings, axis=1)
    norm = np.reshape(norm, (norm.shape[0], 1))
    norm = np.tile(norm, (1, len(embeddings[0])))
    return np.divide(embeddings, norm)


def embed_texts(
        texts: List[str],
        embed_model: str = "bge-large-zh", # 本地embedding模型路径
        to_query: bool = False,
):
    try:
        embeddings = HuggingFaceBgeEmbeddings(model_name=embed_model,
                                              model_kwargs={'device': 'cuda'},
                                              query_instruction='为这个句子生成表示以用于检索相关文章：')
        return embeddings.embed_documents(texts)
    except Exception as e:
        print(f"文本向量化过程中出现错误：{e}")
        return None


def embed_documents(
        docs: List[Document],
        embed_model: str = EMBEDDING_MODEL,
        to_query: bool = False,
) -> Dict:
    """
    将 List[Document] 向量化，转化为 VectorStore.add_embeddings 可以接受的参数
    """
    texts = [x.page_content for x in docs]
    metadatas = [x.metadata for x in docs]
    embeddings = embed_texts(texts=texts, embed_model=embed_model, to_query=to_query)
    if embeddings is not None:
        return {
            "texts": texts,
            "embeddings": embeddings,
            "metadatas": metadatas,
        }


class EmbeddingsFunAdapter(Embeddings):
    def __init__(self, embed_model: str = EMBEDDING_MODEL):
        self.embed_model = embed_model

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        embeddings = embed_texts(texts=texts, embed_model=self.embed_model, to_query=False)
        print(embeddings)
        return normalize(embeddings).tolist()

    def embed_query(self, text: str) -> List[float]:
        embeddings = embed_texts(texts=[text], embed_model=self.embed_model, to_query=True)
        query_embed = embeddings[0]
        query_embed_2d = np.reshape(query_embed, (1, -1))  # 将一维数组转换为二维数组
        normalized_query_embed = normalize(query_embed_2d)
        return normalized_query_embed[0].tolist()  # 将结果转换为一维数组并返回


def search_result2docs(search_results):
    docs = []
    for i in search_results:
        result = search_results[i]
        doc = Document(page_content=result["title"] + " " + result["abs"], metadata={"source":result["url"]})
        docs.append(doc)
    return docs