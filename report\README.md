# 报告文件夹

此文件夹用于存储系统自动生成的报告文件。

## 文件命名规则

报告文件按照以下格式命名：
```
YYYY-MM-DD_HH-MM-SS.md
```

例如：
- `2025-07-03_16-14-30.md` - 2025年7月3日16时14分30秒生成的报告

## 报告生成方式

报告通过以下API端点生成：
```
POST /report
```

请求参数：
- `sessionId`: 会话ID
- `history`: 对话历史
- `compare`: 是否启用对比分析（可选）

## 文件内容

每个报告文件包含：
- 基于对话历史的智能分析
- 数据推演结果
- 政策建议和改进措施
- Markdown格式的结构化内容

## 配置

报告保存路径在 `configs/app_config.py` 中配置：
```python
REPORT_DIR = "report"
```

## 注意事项

- 报告文件会自动保存，无需手动管理
- 文件名使用时间戳确保唯一性
- 支持自定义模板格式
- 建议定期清理旧的报告文件以节省存储空间
