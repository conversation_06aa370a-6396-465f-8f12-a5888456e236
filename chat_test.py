# -*- coding: utf-8 -*-
from openai import OpenAI
import pandas as pd
from utils.common_utils import parse_data
import time
import re

region = "保定市"

# 处理数据
indicators = ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
datafiles = ['symbol_value.csv', 'symbol_relationship.csv', 'symbol_value_deduction.csv']


default_data_file_path = datafiles[0]
unit_file_path = datafiles[1]
tuned_data_file_path = datafiles[2] if len(datafiles) > 2 else None

# 根据变量名取单位
df = pd.read_csv(unit_file_path)
units = {}
for indicator in indicators:
    row = df[df['变量名'] == indicator]

    if not row.empty:
        unit_value = row['单位'].values[0]
        units[indicator] = unit_value
    else:
        units[indicator] = ""
print(f"units: {units}")

default_data_frame = pd.read_csv(default_data_file_path)
default_columns = default_data_frame.columns
default_years = default_data_frame[default_columns[0]]

if tuned_data_file_path:
    tuned_data_frame = pd.read_csv(tuned_data_file_path)
    tuned_columns = tuned_data_frame.columns
    tuned_years = tuned_data_frame[tuned_columns[0]]

default_data = {}
tuned_data = {}

for indicator in indicators:

    if indicator in default_columns:
        default_data[indicator] = {
            "data": parse_data(default_years, default_data_frame[indicator]),
            "unit": units[indicator]
        }
    if tuned_data_file_path and indicator in tuned_columns:
        tuned_data[indicator] = {
            "data": parse_data(tuned_years, tuned_data_frame[indicator]),
            "unit": units[indicator]
        }

print( f"""已知{region}有以下指标项的推演数据：{default_data}，根据输入完成报告：
                标题，政策模拟推演分析报告。
                背景，写一段文字，内容包括对数据中各个指标项的定义，指标项用于反映什么场景的现状，有什么意义。
                数据分析，用一段文字详细总结一下各个指标项的数据的变化趋势。
                总结，提出明确的建议或策略以改进或提升指标的表现。指出具体的行动步骤和预期的效果。
                请确保报告遵循上述结构和格式要求，以保证内容的清晰度和连贯性。
                """)
def chat(llm, key, url, history, query):
    prompt = f"已知{region}有以下指标推演数据：{default_data}"
    if tuned_data:
        prompt += f"，以及优化后的推演结果：{tuned_data}"

    # 调用模型
    client = OpenAI(api_key=key,
                    base_url=url)
    messages = [{'role': 'system', 'content': prompt}]
    for i in range(0, len(history), 2):
        pair = history[i:i + 2]
        messages.append({'role': 'user', 'content': history[i]})
        messages.append({'role': 'assistant', 'content': history[i+1]})
    messages.append({'role': 'user', 'content': query})
    completion = client.chat.completions.create(model=llm, messages=messages,
                                                temperature=0.6, top_p=0.8,
                                                max_tokens=10000)
    response = completion.choices[0].message.content

    # 内容格式化
    # 不同大模型返回结果中可能存在特殊标记，添加针对deepseek的清理
    response = re.sub(r'<think>.*?</think>\s*', '', response, flags=re.DOTALL)

    print(f"response: {response}")

    return response
    

def test(llm, key, url):

    history = []
    
    start_time = time.time()

    print(f"testing: {llm}")

    s1 = "请列出数据中有哪些指标"

    res1 = chat(llm,key,url,history,s1)
    print(res1)

    t1 = time.time()
    t = t1 - start_time
    print(f"问答1: {t:.4f}s")
    history.append(s1)
    history.append(res1)

    s2 = "请问数据对哪个地区多长时间段内的数据进行了推演？"
    res2 = chat(llm,key,url,history,s2)
    print(res2)

    t2 = time.time()
    t = t2 - t1
    print(f"问答2: {t:.4f}s")
    history.append(s2)
    history.append(res2)
 
    prompt = f"""已知{region}有以下指标项的推演数据：{default_data}，根据输入完成报告：
                标题，政策模拟推演分析报告。
                背景，写一段文字，内容包括对数据中各个指标项的定义，指标项用于反映什么场景的现状，有什么意义。
                数据分析，用一段文字详细总结一下各个指标项的数据的变化趋势。
                总结，提出明确的建议或策略以改进或提升指标的表现。指出具体的行动步骤和预期的效果。
                请确保报告遵循上述结构和格式要求，以保证内容的清晰度和连贯性。
                """

    client = OpenAI(api_key=key,
                    base_url=url)
    messages = [{'role': 'system', 'content': '你是一个城市仿真推演相关的数据分析专家，精通数据分析以及报告撰写'}]
    for i in range(0, len(history), 2):
        pair = history[i:i + 2]
        messages.append({'role': 'user', 'content': history[i]})
        messages.append({'role': 'assistant', 'content': history[i+1]})
    messages.append({'role': 'user', 'content': prompt})
    completion = client.chat.completions.create(model=llm, messages=messages,
                                                temperature=0.6, top_p=0.8,
                                                max_tokens=10000)
    response = completion.choices[0].message.content

    print(response)

    end_time = time.time()
    t = end_time - t2
    print(f"生成报告:{t:.4f}s")


models = {
    "gpt-4o-ca": {
        "key": "sk-lHgmTzqBX0G1OwleqPP0LzmFzbEPVxkl0GY8l6NaN4Dd05F0",
        "url": "https://api.chatanywhere.tech/v1"
    },
    # "deepseek-r1:7b": {
    #     "key": "ollama",
    #     "url": "http://192.168.1.58:31435/v1"
    # },
    # "qwen2:7b-instruct": {
    #     "key": "ollama",
    #     "url": "http://192.168.1.58:31435/v1"
    # },
    "deepseek-ai/DeepSeek-R1": {
        "key": "sk-xtbikqyekmbmdrxtcdzyblynkgxofpacrljoibiknixyczvu",
        "url": "https://api.siliconflow.cn/v1"
    }
    # ,
    # "deepseek-r1:70b": {
    #     "key": "ollama",
    #     "url": "http://192.168.1.58:31435/v1"
    # }
}

for llm in models:
    test(llm, models[llm]['key'], models[llm]['url'])
