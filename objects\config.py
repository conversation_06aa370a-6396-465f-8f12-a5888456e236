# -*- coding: utf-8 -*-
import os
import json


class Config:
    """
    全局配置类
    """
    def __init__(self, logger):
        self.logger = logger
        self.configs = self.load_json_config("app_config.json")

    def load_json_config(self, filepath: str) -> dict:
        """
        加载json配置
        """
        try:
            with open(os.path.join("../configs", filepath), 'r') as f:
                config = json.load(f)
                return config
        except IOError as e:
            self.logger.error(f"error occurred while loading config file: {e.strerror}")
            return None

    def get_config(self, config_name: str):
        """
        获取指定配置项
        :param config_name: 配置项名称
        :return:
        """
        return self.configs.get(config_name)
