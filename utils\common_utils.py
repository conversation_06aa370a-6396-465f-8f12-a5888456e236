# -*- coding: utf-8 -*-


def parse_data(years, data):
    """
    解析数据变化
    """
    parsed_data = [
        {
            'year': years[0],
            'data': data[0],
            'change': 0,
            'percentage_change': 0
        }
    ]

    for i in range(1, len(years)):
        prev_value = data[i-1]
        current_year = years[i]
        current_value = data[i]
        change = current_value - prev_value
        percentage_change = (change / prev_value) * 100 if prev_value != 0 else float('inf')
        parsed_data.append({
            'year': current_year,
            'data': current_value,
            'change': change,
            'percentage_change': percentage_change
        })

    return parsed_data
