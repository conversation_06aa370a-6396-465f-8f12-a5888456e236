# -*- coding: utf-8 -*-
import os.path

from flask import Flask, request
import logging.config
import argparse
import sys

from objects.result import Result
from objects.chat_manager import ChatManager
from objects.exceptions import ParameterInvalidException

from configs.app_config import LOG_DIR, LLM_APIS

from local_model import model

app = Flask(__name__)
app.secret_key = 'supersecretkey'

# 导入并注册蓝图
from knowledge_base.doc_management.doc_server import doc_blueprint
app.register_blueprint(doc_blueprint, url_prefix='')


@app.errorhandler(Exception)
def handle_exception(e):
    if isinstance(e, ParameterInvalidException):
        msg = f"参数校验异常，{str(e)}"
        result = Result.invalid(msg, {})
    else:
        msg = f"系统内部异常，{str(e)}"
        result = Result.error(msg, {})
    # 记录异常日志
    app.logger.error(f"发生异常: {msg}, Path: {request.path}")
    return result


@app.route('/models', methods=['POST'])
def models():
    # data = request.get_json()
    app.logger.info("get models")
    # llm_models = ["gpt-4o-ca", "gpt-4o-2024-05-13", "local"]
    llm_models = list(LLM_APIS.keys()) + ["local"]
    return Result.success("请求成功", {"models": llm_models})


@app.route('/initModel', methods=['POST'])
def init_model():
    params = request.get_json()
    app.logger.info(f"request: /initModel, params: {params}")
    if "sessionId" not in params or not params["sessionId"]:
        raise ParameterInvalidException("sessionId不能为空")
    bot_id = params.pop("sessionId")
    manager.create_bot(bot_id, params)
    return Result.success("模型初始化完成", {})


@app.route('/chat', methods=['POST'])
def chat():
    params = request.get_json()
    app.logger.info(f"request: /chat, params: {params}")
    if "sessionId" not in params:
        raise ParameterInvalidException("sessionId不能为空")
    bot_id = params.pop("sessionId")
    bot = manager.get_bot(bot_id)
    if bot is None:
        raise Exception(f"未找到{bot_id}对应模型实例，请先初始化模型")
    query = params.get("inputData")
    if not query:
        return Result.success("请求成功", "")
    history = params.get("history")
    ret = bot.chat(query, history)
    #print(history)
    # ret = "<pre><h1>title</h1></br><p>&nbsp;&nbsp;你好</p></pre>"
    return Result.success("请求成功", ret)


@app.route('/report', methods=['POST'])
def report():
    params = request.get_json()
    app.logger.info(f"request: /report, params: {params}")
    if "sessionId" not in params:
        raise ParameterInvalidException("sessionId不能为空")
    bot_id = params.pop("sessionId")
    bot = manager.get_bot(bot_id)
    if bot is None:
        raise Exception(f"未找到{bot_id}对应模型实例，请先初始化模型")
    history = params.get("history")
    compare = True if params.get("compare") == 1 else False
    ret, ret_file  = bot.report(history, compare)
    payload = {
    "content": ret,
    "filePath": ret_file
    }
    app.logger.info(f"报告{ret_file}生成完成")
    return Result.success("报告生成成功", payload)
    # ret = bot.report(history, compare)
    # #print(history)
    # app.logger.info(f"报告{ret}生成完成")
    # return Result.success("报告生成成功", ret)


def init_logging():
    # 创建日志格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 创建处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    if not os.path.exists(LOG_DIR):
        print(f"创建日志目录{LOG_DIR}")
        os.makedirs(LOG_DIR)

    file_handler = logging.handlers.RotatingFileHandler(
        # './logs/server.log', maxBytes=10485760, backupCount=5, encoding='utf-8', delay=False
        f'{LOG_DIR}/server.log', maxBytes=10485760, backupCount=5, encoding='utf-8', delay=False
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # 创建日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)

    server_logger = logging.getLogger('server')
    server_logger.setLevel(logging.INFO)
    server_logger.addHandler(console_handler)
    server_logger.addHandler(file_handler)

    # 防止日志记录器的传播
    server_logger.propagate = False


if __name__ == '__main__':
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="启动 Flask 应用")
    parser.add_argument('--port', type=int, default=8081, help='指定应用端口号')
    args = parser.parse_args()

    # 从配置文件加载日志配置
    # logging.config.fileConfig('./configs/logging.conf')
    init_logging()

    manager = ChatManager(app.logger)

    # 启动应用
    app.run(host="0.0.0.0", port=args.port)