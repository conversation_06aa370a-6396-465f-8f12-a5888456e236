# -*- coding: utf-8 -*-

class Result:
    """
    响应实体类
    """
    def __init__(self, code: int, msg: str, data):
        self.code = code
        self.msg = msg
        self.data = data

    def __repr__(self):
        return f"Result(code={self.code}, msg={self.msg}, data={self.data})"

    def to_dict(self):
        return {
            "code": self.code,
            "msg": self.msg,
            "data": self.data
        }

    @staticmethod
    def success(msg: str, data):
        if data is None:
            data = {}
        return Result(200, msg, data).to_dict()

    @staticmethod
    def error(msg: str, data):
        if data is None:
            data = {}
        return Result(500, msg, data).to_dict()

    @staticmethod
    def invalid(msg: str, data):
        if data is None:
            data = {}
        return Result(601, msg, data).to_dict()