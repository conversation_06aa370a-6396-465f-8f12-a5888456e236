aiohappyeyeballs==2.3.2
aiohttp==3.10.0
aiosignal==1.3.1
annotated-types==0.7.0
anyio==4.4.0
async-timeout==4.0.3
attrs==23.2.0
blinker==1.8.2
certifi==2024.7.4
chardet==5.2.0
charset-normalizer==3.3.2
click==8.1.7
dataclasses-json==0.6.7
distro==1.9.0
exceptiongroup==1.2.2
filelock==3.15.4
Flask==3.0.3
frozenlist==1.4.1
fsspec==2024.6.1
greenlet==3.0.3
h11==0.14.0
httpcore==1.0.5
httpx==0.27.0
huggingface-hub==0.24.3
idna==3.7
importlib_metadata==8.2.0
itsdangerous==2.2.0
Jinja2==3.1.4
jsonpatch==1.33
jsonpointer==3.0.0
langchain==0.2.9
langchain-community==0.2.7
langchain-core==0.2.25
langchain-text-splitters==0.2.2
langsmith==0.1.94
MarkupSafe==2.1.5
marshmallow==3.21.3
multidict==6.0.5
mypy-extensions==1.0.0
numpy==1.26.4
openai==1.35.13
orjson==3.10.6
outcome==1.3.0.post0
packaging==24.1
pandas==2.2.2
pydantic==2.8.2
pydantic_core==2.20.1
PySocks==1.7.1
python-dateutil==2.9.0.post0
pytz==2024.1
PyYAML==6.0.1
regex==2024.7.24
requests==2.32.3
safetensors==0.4.3
selenium==4.22.0
six==1.16.0
sniffio==1.3.1
sortedcontainers==2.4.0
SQLAlchemy==2.0.31
tenacity==8.5.0
tokenizers==0.19.1
tqdm==4.66.4
transformers==4.42.3
trio==0.26.0
trio-websocket==0.11.1
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.1
urllib3==2.2.2
websocket-client==1.8.0
Werkzeug==3.0.3
wsproto==1.2.0
yarl==1.9.4
zipp==3.19.2
