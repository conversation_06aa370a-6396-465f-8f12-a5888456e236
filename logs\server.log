2024-10-13 21:06:44,739 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-10-13 21:06:44,899 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /
2024-10-13 21:06:45,853 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-10-13 21:07:35,892 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-10-13 21:07:36,798 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-10-13 21:07:37,104 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-10-13 21:07:38,019 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-10-13 21:07:38,322 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-10-13 21:07:39,226 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-10-13 21:07:39,535 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-10-13 21:07:40,453 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-10-13 21:07:40,763 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-10-13 21:07:41,619 - server - INFO - request: /doc_server
2024-10-13 21:07:41,679 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-11-29 09:34:51,672 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-11-29 09:34:51,998 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-11-29 09:34:52,902 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-11-29 09:34:53,229 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-11-29 09:34:54,157 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-11-29 09:34:54,387 - server - INFO - request: /doc_server
2024-11-29 09:35:27,896 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-11-29 09:35:28,193 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-11-29 09:35:29,091 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-11-29 09:35:29,599 - server - ERROR - 发生异常: 系统内部异常，405 Method Not Allowed: The method is not allowed for the requested URL., Path: /models
2024-11-29 09:35:30,536 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-11-29 11:13:52,668 - server - INFO - get models
2024-11-29 11:14:05,744 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044/PYSD_project\\data\\resultdata\\exp河北省/csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044/PYSD_project\\data\\resultdata\\exp河北省/csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044/PYSD_project\\data\\resultdata\\exp河北省/csv\\result_data\\PolicyDeduction\\exp河北省1/symbol_value.csv', 'indicators': '', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {}, 'region': '', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 11:14:05,745 - server - ERROR - 发生异常: 参数校验异常，地区不能为空, Path: /initModel
2024-11-29 11:20:24,272 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044/PYSD_project\\data\\resultdata\\exp河北省/csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044/PYSD_project\\data\\resultdata\\exp河北省/csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044/PYSD_project\\data\\resultdata\\exp河北省/csv\\result_data\\PolicyDeduction\\exp河北省1/symbol_value.csv', 'indicators': '', 'knowledgeBase': 'default_kb', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {}, 'region': '', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 11:20:24,274 - server - ERROR - 发生异常: 参数校验异常，地区不能为空, Path: /initModel
2024-11-29 12:06:59,555 - server - INFO - get models
2024-11-29 12:07:10,842 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044/PYSD_project\\data\\resultdata\\exp河北省/csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044/PYSD_project\\data\\resultdata\\exp河北省/csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044/PYSD_project\\data\\resultdata\\exp河北省/csv\\result_data\\PolicyDeduction\\exp河北省1/symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 12:07:10,845 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 12:07:10,846 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-11-29 13:56:13,742 - server - INFO - get models
2024-11-29 13:56:24,674 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {}, 'region': '', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 13:56:24,676 - server - ERROR - 发生异常: 参数校验异常，地区不能为空, Path: /initModel
2024-11-29 13:58:52,318 - server - INFO - get models
2024-11-29 13:59:04,851 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 13:59:04,853 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 13:59:04,856 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-11-29 13:59:26,639 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '1', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-11-29 13:59:54,365 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['1', '请问您需要什么帮助？'], 'inputData': '建筑密度计算公式', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-11-29 14:00:40,540 - server - INFO - request: /report, params: {'compare': 0, 'history': ['1', '请问您需要什么帮助？', '建筑密度计算公式', '建筑密度是指在一定范围内，建筑物的基底面积总和与该范围用地面积的比率。建筑密度的计算公式为：\n\n\\[ \\text{建筑密度} (\\%) = \\left( \\frac{\\text{建筑基底总面积}}{\\text{用地总面积}} \\right) \\times 100\\% \\]\n\n其中：\n- 建筑基底总面积是指所有建筑物的基底占地面积的总和。\n- 用地总面积是指计算范围内的总用地面积。'], 'sessionId': 'admin_run_43'}
2024-11-29 14:02:26,514 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份未成年人口占比, 初始当前省份老年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 14:02:26,516 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 14:02:26,519 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份未成年人口占比', '初始当前省份老年人口占比']
2024-11-29 14:02:43,126 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份未成年人口占比, 死亡率10至14岁', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 14:02:43,128 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 14:02:43,132 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份未成年人口占比', '死亡率10至14岁']
2024-11-29 14:22:28,628 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-11-29 14:22:28,912 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-11-29 14:23:14,863 - server - INFO - get models
2024-11-29 14:37:04,683 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 14:37:04,686 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 14:37:04,689 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-11-29 14:37:57,396 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份未成年人口占比, 死亡率55至59岁', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 14:37:57,398 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 14:37:57,402 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份未成年人口占比', '死亡率55至59岁']
2024-11-29 14:39:09,257 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 14:39:09,260 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 14:39:09,263 - server - INFO - indicators: ['死亡人口']
2024-11-29 14:40:58,265 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 14:40:58,268 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 14:40:58,271 - server - INFO - indicators: ['死亡人口']
2024-11-29 14:41:34,248 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 14:41:34,251 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 14:41:34,254 - server - INFO - indicators: ['死亡人口']
2024-11-29 14:45:45,523 - server - INFO - get models
2024-11-29 14:46:42,907 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 历史死亡率, 历史出生率', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 14:46:42,908 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 14:46:42,913 - server - INFO - indicators: ['死亡人口', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '历史死亡率', '历史出生率']
2024-11-29 14:47:09,433 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 历史死亡率, 历史出生率', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 14:47:09,435 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 14:47:09,439 - server - INFO - indicators: ['死亡人口', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '历史死亡率', '历史出生率']
2024-11-29 14:47:31,514 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 14:47:31,515 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 14:47:31,519 - server - INFO - indicators: ['死亡人口', 'GDP']
2024-11-29 15:01:12,250 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '数据中包含什么指标', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-11-29 15:13:08,877 - server - INFO - request: /report, params: {'compare': 0, 'history': ['数据中包含什么指标', '数据中包含两个指标：死亡人口和GDP。'], 'sessionId': 'admin_run_43'}
2024-11-29 15:21:31,824 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 15:21:31,826 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 15:21:31,827 - server - INFO - indicators: ['死亡人口', 'GDP']
2024-11-29 15:30:25,325 - server - INFO - get models
2024-11-29 15:30:45,917 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 15:30:45,920 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 15:30:45,921 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-11-29 15:32:54,419 - server - INFO - get models
2024-11-29 15:33:16,579 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 15:33:16,581 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 15:33:16,582 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-11-29 15:34:12,115 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 15:34:12,117 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 15:34:12,119 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-11-29 15:44:00,676 - server - INFO - get models
2024-11-29 15:45:24,886 - server - INFO - get models
2024-11-29 15:45:37,237 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 15:45:37,239 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 15:45:37,240 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-11-29 15:45:45,491 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-11-29 15:45:45,493 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-11-29 15:45:45,494 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-11-29 16:19:14,036 - server - INFO - get models
2024-11-29 16:22:51,433 - server - INFO - get models
2024-11-29 16:24:05,852 - server - INFO - get models
2024-11-29 16:25:13,439 - server - INFO - get models
2024-11-29 16:25:17,548 - server - INFO - get models
2024-11-29 16:25:31,010 - server - INFO - get models
2024-11-29 16:26:16,499 - server - INFO - get models
2024-11-29 16:27:13,499 - server - INFO - get models
2024-11-29 16:27:34,894 - server - INFO - get models
2024-11-29 16:28:37,414 - server - INFO - get models
2024-11-29 16:29:21,857 - server - INFO - get models
2024-11-29 16:30:05,651 - server - INFO - get models
2024-11-29 16:31:34,622 - server - INFO - get models
2024-11-29 16:32:39,699 - server - INFO - get models
2024-11-29 16:41:04,756 - server - INFO - get models
2024-12-01 12:49:59,102 - server - INFO - get models
2024-12-01 13:34:38,245 - server - INFO - get models
2024-12-01 13:35:12,995 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-01 13:35:13,012 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-01 13:35:13,012 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-12-01 13:35:33,836 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-01 13:35:33,838 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-01 13:35:33,839 - server - INFO - indicators: ['死亡人口', 'GDP']
2024-12-01 13:35:47,045 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '什么是GDP', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-12-01 13:36:33,613 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-01 13:36:33,616 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-01 13:36:33,617 - server - INFO - indicators: ['死亡人口', 'GDP']
2024-12-01 13:36:51,821 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-01 13:36:51,824 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-01 13:36:51,824 - server - INFO - indicators: ['死亡人口', 'GDP']
2024-12-01 13:37:01,573 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP', 'knowledgeBase': 'default_kb', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-01 13:37:01,575 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-01 13:37:01,576 - server - INFO - indicators: ['死亡人口', 'GDP']
2024-12-01 13:37:19,276 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-01 13:37:19,278 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-01 13:37:19,279 - server - INFO - indicators: ['死亡人口', 'GDP']
2024-12-01 13:37:37,712 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-01 13:37:37,713 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-01 13:37:37,714 - server - INFO - indicators: ['死亡人口', 'GDP']
2024-12-01 13:37:55,529 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-01 13:37:55,531 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-01 13:37:55,532 - server - INFO - indicators: ['死亡人口', 'GDP']
2024-12-01 13:39:22,411 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-01 13:39:22,414 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-01 13:39:22,415 - server - INFO - indicators: ['死亡人口', 'GDP']
2024-12-01 13:40:13,422 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省1\\symbol_value.csv', 'indicators': '死亡人口, GDP', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-01 13:40:13,424 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-01 13:40:13,424 - server - INFO - indicators: ['死亡人口', 'GDP']
2024-12-01 13:40:26,075 - server - INFO - request: /report, params: {'compare': 1, 'history': [], 'sessionId': 'admin_run_43'}
2024-12-01 13:58:46,794 - server - INFO - get models
2024-12-02 08:51:38,442 - server - INFO - get models
2024-12-02 08:55:30,014 - server - INFO - get models
2024-12-02 08:55:55,409 - server - INFO - get models
2024-12-02 10:15:45,832 - server - INFO - get models
2024-12-02 10:17:49,984 - server - INFO - get models
2024-12-02 10:19:09,463 - server - INFO - get models
2024-12-02 10:19:35,410 - server - INFO - get models
2024-12-02 10:20:38,928 - server - INFO - get models
2024-12-02 10:21:37,371 - server - INFO - get models
2024-12-02 10:22:00,312 - server - INFO - get models
2024-12-02 10:34:54,610 - server - INFO - get models
2024-12-02 10:35:39,844 - server - INFO - get models
2024-12-03 16:10:39,918 - server - INFO - get models
2024-12-03 16:26:59,223 - server - INFO - get models
2024-12-03 16:27:12,105 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省4\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-03 16:27:12,106 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-03 16:27:12,108 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-12-03 16:27:19,295 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省4\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-03 16:27:19,297 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-03 16:27:19,298 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-12-03 16:27:24,447 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2024-12-03 17:57:32,561 - server - INFO - get models
2024-12-03 17:57:59,179 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省4\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-03 17:57:59,181 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-03 17:57:59,181 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-12-03 17:58:09,697 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2024-12-04 09:29:29,382 - server - INFO - get models
2024-12-04 10:23:41,981 - server - INFO - get models
2024-12-04 10:23:52,238 - server - INFO - get models
2024-12-04 10:24:31,821 - server - INFO - get models
2024-12-04 10:40:34,250 - server - INFO - get models
2024-12-04 10:57:17,392 - server - INFO - get models
2024-12-04 10:58:36,023 - server - INFO - get models
2024-12-04 10:59:15,103 - server - INFO - get models
2024-12-04 11:31:51,208 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-12-04 11:56:08,155 - server - INFO - get models
2024-12-04 14:36:17,220 - server - INFO - get models
2024-12-04 20:23:01,524 - server - INFO - get models
2024-12-05 08:54:41,093 - server - INFO - get models
2024-12-05 09:10:52,169 - server - INFO - get models
2024-12-05 09:10:59,043 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省6\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-05 09:10:59,045 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-05 09:10:59,045 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-12-05 09:11:20,735 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省6\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-05 09:11:20,737 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-05 09:11:20,738 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-12-05 09:11:52,718 - server - INFO - get models
2024-12-05 09:11:55,152 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省6\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-05 09:11:55,153 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-05 09:11:55,154 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-12-05 09:12:00,328 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2024-12-05 09:12:32,457 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2024-12-05 09:12:46,601 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省6\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-05 09:12:46,602 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-05 09:12:46,603 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-12-05 09:23:54,756 - server - INFO - get models
2024-12-05 09:24:22,089 - server - INFO - get models
2024-12-05 09:32:17,136 - server - INFO - get models
2024-12-05 09:33:30,968 - server - INFO - get models
2024-12-05 09:35:11,237 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省6\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-05 09:35:11,242 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-05 09:35:11,242 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-12-05 09:35:35,830 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2024-12-05 09:57:07,972 - server - INFO - get models
2024-12-05 10:15:30,875 - server - INFO - get models
2024-12-05 10:25:47,157 - server - INFO - get models
2024-12-05 10:25:49,355 - server - INFO - get models
2024-12-05 10:32:36,754 - server - INFO - get models
2024-12-05 10:32:54,013 - server - INFO - get models
2024-12-05 10:33:48,848 - server - INFO - get models
2024-12-05 10:57:31,025 - server - INFO - get models
2024-12-05 12:49:02,043 - server - INFO - get models
2024-12-05 12:49:03,452 - server - INFO - get models
2024-12-05 12:49:09,772 - server - INFO - get models
2024-12-05 12:49:16,081 - server - INFO - get models
2024-12-05 12:49:18,790 - server - INFO - get models
2024-12-05 12:49:27,698 - server - INFO - get models
2024-12-05 12:49:33,407 - server - INFO - get models
2024-12-05 12:49:39,285 - server - INFO - get models
2024-12-05 12:49:40,830 - server - INFO - get models
2024-12-05 12:49:44,031 - server - INFO - get models
2024-12-05 12:49:47,792 - server - INFO - get models
2024-12-05 12:49:50,408 - server - INFO - get models
2024-12-05 12:50:15,444 - server - INFO - get models
2024-12-05 12:50:18,266 - server - INFO - get models
2024-12-05 12:50:26,407 - server - INFO - get models
2024-12-05 12:50:28,579 - server - INFO - get models
2024-12-05 12:50:31,572 - server - INFO - get models
2024-12-05 12:50:36,765 - server - INFO - get models
2024-12-05 12:51:00,984 - server - INFO - get models
2024-12-05 12:51:05,214 - server - INFO - get models
2024-12-05 12:51:08,928 - server - INFO - get models
2024-12-05 12:52:56,331 - server - INFO - get models
2024-12-05 12:53:04,726 - server - INFO - get models
2024-12-05 12:53:07,804 - server - INFO - get models
2024-12-05 12:53:09,858 - server - INFO - get models
2024-12-05 12:53:12,080 - server - INFO - get models
2024-12-05 12:53:15,940 - server - INFO - get models
2024-12-05 12:53:18,010 - server - INFO - get models
2024-12-05 12:53:20,379 - server - INFO - get models
2024-12-05 12:53:24,649 - server - INFO - get models
2024-12-05 12:53:34,436 - server - INFO - get models
2024-12-05 12:53:37,170 - server - INFO - get models
2024-12-05 12:53:39,057 - server - INFO - get models
2024-12-05 12:55:41,110 - server - INFO - get models
2024-12-05 12:56:08,577 - server - INFO - get models
2024-12-05 12:56:10,626 - server - INFO - get models
2024-12-05 12:56:30,644 - server - INFO - get models
2024-12-05 12:56:32,751 - server - INFO - get models
2024-12-05 12:57:14,387 - server - INFO - get models
2024-12-05 12:57:17,780 - server - INFO - get models
2024-12-05 12:57:20,124 - server - INFO - get models
2024-12-05 12:57:21,490 - server - INFO - get models
2024-12-05 12:57:23,913 - server - INFO - get models
2024-12-05 12:57:26,305 - server - INFO - get models
2024-12-05 12:57:29,470 - server - INFO - get models
2024-12-05 12:57:44,440 - server - INFO - get models
2024-12-05 13:10:47,661 - server - INFO - get models
2024-12-05 14:03:42,903 - server - INFO - get models
2024-12-05 14:03:47,310 - server - INFO - get models
2024-12-05 14:06:56,876 - server - INFO - get models
2024-12-05 14:08:03,772 - server - INFO - get models
2024-12-05 14:08:05,959 - server - INFO - get models
2024-12-05 14:08:08,493 - server - INFO - get models
2024-12-05 14:23:57,912 - server - INFO - get models
2024-12-05 14:51:47,268 - server - INFO - get models
2024-12-05 14:52:19,839 - server - INFO - get models
2024-12-05 14:52:33,263 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省6\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-05 14:52:33,264 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-05 14:52:33,265 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-12-05 14:53:42,048 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省6\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '1', 'topP': 0.8}
2024-12-05 14:53:42,052 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-05 14:53:42,052 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-12-05 14:53:42,119 - server - ERROR - 发生异常: 参数校验异常，模板文件仅支持txt、markdown格式, Path: /initModel
2024-12-05 14:54:08,288 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2024-12-05 14:55:10,039 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '返回一个对比结果', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-12-05 16:51:20,893 - server - INFO - get models
2024-12-05 16:52:18,713 - server - INFO - get models
2024-12-05 16:52:24,619 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp河北省\\csv\\result_data\\PolicyDeduction\\exp河北省7\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 基础死亡率, 初始当前省份成年人口占比, 初始当前省份未成年人口占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份老年人口占比': 1, '未成年死亡率': 1, '老年人死亡率': 1}, 'region': '河北省', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-05 16:52:24,621 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-05 16:52:24,622 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '基础死亡率', '初始当前省份成年人口占比', '初始当前省份未成年人口占比']
2024-12-05 16:52:42,220 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2024-12-05 16:53:06,765 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '生成一个报告结果', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-12-05 16:54:56,156 - server - INFO - get models
2024-12-06 09:34:34,404 - server - INFO - get models
2024-12-06 12:17:13,207 - server - INFO - get models
2024-12-09 09:50:46,659 - server - INFO - get models
2024-12-09 09:51:23,727 - server - INFO - get models
2024-12-09 09:51:42,574 - server - INFO - get models
2024-12-09 09:53:47,572 - server - INFO - get models
2024-12-09 16:26:15,630 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-12-09 16:45:46,270 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2024-12-10 11:10:26,499 - server - INFO - get models
2024-12-10 16:32:20,822 - server - INFO - get models
2024-12-10 16:35:14,404 - server - INFO - get models
2024-12-18 19:55:02,478 - server - INFO - get models
2024-12-18 19:55:37,412 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市8\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-18 19:55:37,414 - server - ERROR - 发生异常: 参数校验异常，地区不能为空, Path: /initModel
2024-12-18 19:59:45,663 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市8\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-18 19:59:45,666 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-18 19:59:45,669 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-18 20:00:36,206 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市8\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-18 20:00:36,208 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-18 20:00:36,209 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-18 20:01:41,746 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市8\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-18 20:01:41,748 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-18 20:01:41,748 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-18 20:26:13,351 - server - INFO - get models
2024-12-18 20:26:24,467 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市8\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-18 20:26:24,469 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-18 20:26:24,470 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-18 20:26:24,540 - server - ERROR - 发生异常: 系统内部异常，'utf-8' codec can't decode byte 0xb1 in position 3: invalid start byte, Path: /initModel
2024-12-18 20:26:42,143 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市8\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-18 20:26:42,144 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-18 20:26:42,145 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-18 20:26:42,213 - server - ERROR - 发生异常: 系统内部异常，'utf-8' codec can't decode byte 0xb1 in position 3: invalid start byte, Path: /initModel
2024-12-18 20:28:55,887 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市8\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-18 20:28:55,889 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-18 20:28:55,893 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-18 20:30:58,037 - server - INFO - get models
2024-12-18 20:31:25,468 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市8\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-18 20:31:25,468 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-18 20:31:25,468 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-18 20:32:43,678 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市8\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-18 20:32:43,680 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-18 20:32:43,687 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-18 20:33:15,793 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2024-12-19 09:25:53,344 - server - INFO - get models
2024-12-19 09:26:13,939 - server - INFO - get models
2024-12-19 11:40:31,254 - server - INFO - get models
2024-12-19 11:41:41,451 - server - INFO - get models
2024-12-19 11:42:39,156 - server - INFO - get models
2024-12-19 11:46:07,145 - server - INFO - get models
2024-12-19 15:10:48,317 - server - INFO - get models
2024-12-19 15:10:50,396 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市10\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-19 15:10:50,398 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-19 15:10:50,399 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-19 15:10:55,601 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '123', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-12-19 15:14:31,167 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市10\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-19 15:14:31,169 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-19 15:14:31,173 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-19 15:14:36,205 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '333', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-12-19 15:15:51,786 - server - INFO - get models
2024-12-19 15:47:06,108 - server - INFO - get models
2024-12-19 15:47:10,951 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市10\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-19 15:47:10,952 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-19 15:47:10,959 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-19 15:47:33,807 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '数据中有哪些指标', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-12-19 15:48:19,117 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['数据中有哪些指标', '数据中包含以下指标：\n\n1. 死亡人口\n2. GDP（国内生产总值）\n3. 历史常住人口\n4. 历史出生率\n5. 历史死亡率\n6. 历史平均预期寿命'], 'inputData': '分析一下死亡人口的变化趋势', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-12-19 15:51:53,705 - server - INFO - get models
2024-12-20 10:41:57,186 - server - INFO - get models
2024-12-20 10:46:10,057 - server - INFO - get models
2024-12-20 13:02:11,772 - server - INFO - get models
2024-12-20 13:32:54,368 - server - INFO - get models
2024-12-20 13:39:00,947 - server - INFO - get models
2024-12-20 13:40:03,811 - server - INFO - get models
2024-12-20 13:40:13,606 - server - INFO - get models
2024-12-20 13:42:12,190 - server - INFO - get models
2024-12-20 13:47:05,008 - server - INFO - get models
2024-12-20 13:50:31,678 - server - INFO - get models
2024-12-20 13:50:33,521 - server - INFO - get models
2024-12-20 13:50:37,342 - server - INFO - get models
2024-12-20 13:50:39,817 - server - INFO - get models
2024-12-20 16:09:11,023 - server - INFO - get models
2024-12-20 16:09:27,699 - server - INFO - get models
2024-12-20 16:09:49,683 - server - INFO - get models
2024-12-20 17:54:42,306 - server - INFO - get models
2024-12-21 08:14:30,145 - server - INFO - get models
2024-12-21 11:15:09,803 - server - INFO - get models
2024-12-21 15:17:49,411 - server - INFO - get models
2024-12-21 15:17:53,997 - server - INFO - get models
2024-12-21 15:18:04,475 - server - INFO - get models
2024-12-21 15:22:49,893 - server - INFO - get models
2024-12-21 15:22:55,405 - server - INFO - get models
2024-12-21 15:45:42,896 - server - INFO - get models
2024-12-21 15:45:47,731 - server - INFO - get models
2024-12-21 15:54:02,386 - server - INFO - get models
2024-12-21 15:54:06,099 - server - INFO - get models
2024-12-21 15:54:41,355 - server - INFO - get models
2024-12-21 16:01:11,135 - server - INFO - get models
2024-12-21 16:06:47,812 - server - INFO - get models
2024-12-21 16:06:51,828 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-21 16:06:51,829 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-21 16:06:51,830 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-21 16:06:51,858 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2024-12-21 16:15:05,944 - server - INFO - get models
2024-12-21 16:26:37,678 - server - INFO - get models
2024-12-22 09:31:16,499 - server - INFO - get models
2024-12-22 09:33:58,411 - server - INFO - get models
2024-12-22 09:36:12,962 - server - INFO - get models
2024-12-22 09:36:34,758 - server - INFO - get models
2024-12-22 10:15:50,273 - server - INFO - get models
2024-12-22 10:49:42,703 - server - INFO - get models
2024-12-23 08:33:55,338 - server - INFO - get models
2024-12-23 08:33:58,804 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-23 08:33:58,805 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-23 08:33:58,809 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-23 08:34:02,989 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '1', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-12-23 08:34:12,679 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-23 08:34:12,680 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-23 08:34:12,685 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-23 08:34:16,944 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '你说', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-12-23 08:34:23,767 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['你说', '当然，请问有什么我可以帮助您的吗？'], 'inputData': '你是谁', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-12-23 08:34:32,007 - server - INFO - request: /report, params: {'compare': 0, 'history': ['你说', '当然，请问有什么我可以帮助您的吗？', '你是谁', '我是一个由OpenAI训练的人工智能助手，旨在帮助回答问题和提供信息。无论您有什么问题或需要什么帮助，请随时告诉我！'], 'sessionId': 'admin_run_43'}
2024-12-23 08:35:09,140 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-23 08:35:09,141 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-23 08:35:09,146 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-23 08:35:52,842 - server - INFO - request: /report, params: {'compare': 1, 'history': [], 'sessionId': 'admin_run_43'}
2024-12-23 08:36:16,857 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '分析一下常住人口', 'outputType': 1, 'sessionId': 'admin_run_43'}
2024-12-23 08:49:37,793 - server - INFO - get models
2024-12-23 08:49:39,976 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-23 08:49:39,978 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-23 08:49:39,978 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-23 08:49:40,005 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2024-12-23 08:50:00,776 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-23 08:50:00,778 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-23 08:50:00,778 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-23 08:50:00,803 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2024-12-23 09:06:14,789 - server - INFO - get models
2024-12-23 10:20:18,012 - server - INFO - get models
2024-12-23 10:55:11,131 - server - INFO - get models
2024-12-23 10:55:15,941 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市2\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-23 10:55:15,943 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-23 10:55:15,943 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-23 10:55:31,586 - server - INFO - get models
2024-12-23 10:55:35,282 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市2\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-23 10:55:35,284 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-23 10:55:35,284 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-23 10:55:44,117 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2024-12-23 14:34:37,314 - server - INFO - get models
2024-12-23 14:35:31,401 - server - INFO - get models
2024-12-23 15:11:32,922 - server - INFO - get models
2024-12-23 15:44:23,667 - server - INFO - get models
2024-12-23 15:46:23,486 - server - INFO - get models
2024-12-23 16:09:01,883 - server - INFO - get models
2024-12-23 16:09:07,436 - server - INFO - get models
2024-12-23 16:09:09,401 - server - INFO - get models
2024-12-23 16:10:30,013 - server - INFO - get models
2024-12-23 16:13:38,060 - server - INFO - get models
2024-12-23 16:14:51,061 - server - INFO - get models
2024-12-23 16:14:53,871 - server - INFO - get models
2024-12-23 16:20:07,202 - server - INFO - get models
2024-12-23 16:20:16,276 - server - INFO - get models
2024-12-23 16:20:18,784 - server - INFO - get models
2024-12-23 16:22:11,007 - server - INFO - get models
2024-12-23 16:22:12,519 - server - INFO - get models
2024-12-23 16:24:17,516 - server - INFO - get models
2024-12-23 16:24:19,413 - server - INFO - get models
2024-12-23 17:21:38,618 - server - INFO - get models
2024-12-23 17:21:57,449 - server - INFO - get models
2024-12-23 17:25:20,308 - server - INFO - get models
2024-12-23 17:25:54,576 - server - INFO - get models
2024-12-23 17:28:06,512 - server - INFO - get models
2024-12-23 17:28:19,375 - server - INFO - get models
2024-12-23 17:32:02,221 - server - INFO - get models
2024-12-23 17:32:06,951 - server - INFO - get models
2024-12-23 17:35:45,369 - server - INFO - get models
2024-12-23 19:26:29,013 - server - INFO - get models
2024-12-23 19:27:08,496 - server - INFO - get models
2024-12-23 19:27:14,142 - server - INFO - get models
2024-12-23 20:16:21,558 - server - INFO - get models
2024-12-23 20:21:29,337 - server - INFO - get models
2024-12-23 20:22:02,739 - server - INFO - get models
2024-12-23 20:25:37,456 - server - INFO - get models
2024-12-24 08:48:16,545 - server - INFO - get models
2024-12-24 08:48:35,805 - server - INFO - get models
2024-12-24 09:22:36,716 - server - INFO - get models
2024-12-24 09:43:37,179 - server - INFO - get models
2024-12-24 10:29:40,075 - server - INFO - get models
2024-12-24 10:30:49,952 - server - INFO - get models
2024-12-24 10:58:22,273 - server - INFO - get models
2024-12-24 10:59:26,253 - server - INFO - get models
2024-12-24 10:59:40,498 - server - INFO - get models
2024-12-24 11:00:41,383 - server - INFO - get models
2024-12-24 11:00:45,751 - server - INFO - get models
2024-12-24 11:00:54,379 - server - INFO - get models
2024-12-24 11:01:36,409 - server - INFO - get models
2024-12-24 11:02:09,148 - server - INFO - get models
2024-12-24 11:02:56,510 - server - INFO - get models
2024-12-24 11:03:13,255 - server - INFO - get models
2024-12-24 11:03:31,622 - server - INFO - get models
2024-12-24 11:03:40,871 - server - INFO - get models
2024-12-24 11:05:55,032 - server - INFO - get models
2024-12-24 11:06:34,986 - server - INFO - get models
2024-12-24 11:06:47,299 - server - INFO - get models
2024-12-24 11:07:26,526 - server - INFO - get models
2024-12-24 11:09:21,967 - server - INFO - get models
2024-12-24 11:09:35,342 - server - INFO - get models
2024-12-24 11:43:10,270 - server - INFO - get models
2024-12-24 11:57:44,084 - server - INFO - get models
2024-12-24 12:40:44,557 - server - INFO - get models
2024-12-24 12:43:44,885 - server - INFO - get models
2024-12-24 14:19:28,399 - server - INFO - get models
2024-12-24 15:05:04,639 - server - INFO - get models
2024-12-24 15:05:14,339 - server - INFO - get models
2024-12-24 15:05:28,987 - server - INFO - get models
2024-12-24 15:10:19,583 - server - INFO - get models
2024-12-24 15:11:19,624 - server - INFO - get models
2024-12-24 16:39:50,792 - server - INFO - get models
2024-12-24 16:40:22,750 - server - INFO - get models
2024-12-24 16:41:42,475 - server - INFO - get models
2024-12-24 16:43:08,401 - server - INFO - get models
2024-12-24 16:51:49,828 - server - INFO - get models
2024-12-24 16:53:06,279 - server - INFO - get models
2024-12-24 16:53:26,316 - server - INFO - get models
2024-12-24 16:53:36,427 - server - INFO - get models
2024-12-24 16:54:08,320 - server - INFO - get models
2024-12-24 16:54:32,578 - server - INFO - get models
2024-12-24 16:58:40,630 - server - INFO - get models
2024-12-24 16:59:52,273 - server - INFO - get models
2024-12-24 17:01:35,672 - server - INFO - get models
2024-12-24 18:09:07,243 - server - INFO - get models
2024-12-24 18:11:57,534 - server - INFO - get models
2024-12-24 18:12:53,247 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-24 18:12:53,249 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-24 18:12:53,249 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-24 18:12:53,277 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2024-12-24 18:14:11,037 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-24 18:14:11,039 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-24 18:14:11,039 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-24 18:14:11,065 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2024-12-25 09:03:04,476 - server - INFO - get models
2024-12-25 09:03:07,291 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-25 09:03:07,293 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-25 09:03:07,293 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-25 09:03:07,322 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2024-12-25 09:06:33,790 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2024-12-25 09:06:33,792 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2024-12-25 09:06:33,793 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2024-12-25 09:06:33,818 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2024-12-25 09:06:47,383 - server - INFO - get models
2024-12-25 09:07:03,995 - server - INFO - get models
2024-12-25 09:07:31,512 - server - INFO - get models
2024-12-25 09:18:44,640 - server - INFO - get models
2024-12-25 09:20:04,988 - server - INFO - get models
2024-12-25 13:31:16,093 - server - INFO - get models
2024-12-25 13:31:38,742 - server - INFO - get models
2024-12-25 13:32:41,990 - server - INFO - get models
2024-12-25 13:34:50,207 - server - INFO - get models
2024-12-25 13:35:37,843 - server - INFO - get models
2024-12-25 13:35:49,183 - server - INFO - get models
2024-12-25 13:36:09,613 - server - INFO - get models
2024-12-25 13:37:57,342 - server - INFO - get models
2024-12-25 13:43:30,279 - server - INFO - get models
2024-12-25 13:45:59,910 - server - INFO - get models
2024-12-25 13:49:47,254 - server - INFO - get models
2024-12-25 13:49:55,385 - server - INFO - get models
2024-12-25 13:49:56,708 - server - INFO - get models
2024-12-25 13:52:02,312 - server - INFO - get models
2024-12-25 14:01:02,478 - server - INFO - get models
2024-12-26 11:30:04,647 - server - INFO - get models
2024-12-26 11:30:11,102 - server - INFO - get models
2024-12-26 11:30:15,956 - server - INFO - get models
2024-12-27 14:34:09,338 - server - INFO - get models
2025-01-02 10:09:47,603 - server - INFO - get models
2025-01-02 10:37:52,684 - server - INFO - get models
2025-01-02 10:53:31,180 - server - INFO - get models
2025-01-02 10:55:50,713 - server - INFO - get models
2025-01-02 11:02:57,174 - server - INFO - get models
2025-01-02 11:13:59,127 - server - INFO - get models
2025-01-02 11:15:50,310 - server - INFO - get models
2025-01-02 11:30:38,953 - server - INFO - get models
2025-01-02 11:38:29,211 - server - INFO - get models
2025-01-02 11:38:36,167 - server - INFO - get models
2025-01-02 11:39:06,472 - server - INFO - get models
2025-01-02 11:48:11,071 - server - INFO - get models
2025-01-02 16:47:39,434 - server - INFO - get models
2025-01-02 16:51:42,694 - server - INFO - get models
2025-01-02 16:55:07,474 - server - INFO - get models
2025-01-02 16:58:17,726 - server - INFO - get models
2025-01-02 16:58:32,844 - server - INFO - get models
2025-01-02 18:24:14,842 - server - INFO - get models
2025-01-02 18:25:17,709 - server - INFO - get models
2025-01-02 18:25:20,412 - server - INFO - get models
2025-01-02 18:25:33,601 - server - INFO - get models
2025-01-02 18:27:41,059 - server - INFO - get models
2025-01-02 18:27:44,517 - server - INFO - get models
2025-01-02 18:29:51,540 - server - INFO - get models
2025-01-02 18:30:44,849 - server - INFO - get models
2025-01-02 18:34:13,554 - server - INFO - get models
2025-01-02 18:34:42,505 - server - INFO - get models
2025-01-02 18:34:55,064 - server - INFO - get models
2025-01-02 18:35:59,629 - server - INFO - get models
2025-01-02 19:07:44,927 - server - INFO - get models
2025-01-02 19:08:52,381 - server - INFO - get models
2025-01-02 19:13:42,806 - server - INFO - get models
2025-01-02 19:18:07,491 - server - INFO - get models
2025-01-02 19:18:12,168 - server - INFO - get models
2025-01-02 19:18:16,495 - server - INFO - get models
2025-01-02 19:18:17,990 - server - INFO - get models
2025-01-02 19:18:27,663 - server - INFO - get models
2025-01-02 19:21:31,222 - server - INFO - get models
2025-01-02 19:22:10,493 - server - INFO - get models
2025-01-02 19:22:14,082 - server - INFO - get models
2025-01-02 19:24:20,934 - server - INFO - get models
2025-01-02 19:26:45,411 - server - INFO - get models
2025-01-02 19:27:05,790 - server - INFO - get models
2025-01-02 19:27:23,942 - server - INFO - get models
2025-01-02 19:27:48,576 - server - INFO - get models
2025-01-02 19:29:37,759 - server - INFO - get models
2025-01-02 19:29:42,281 - server - INFO - get models
2025-01-02 19:29:45,954 - server - INFO - get models
2025-01-02 20:04:21,327 - server - INFO - get models
2025-01-02 20:06:16,517 - server - INFO - get models
2025-01-02 20:07:43,938 - server - INFO - get models
2025-01-02 20:07:53,649 - server - INFO - get models
2025-01-02 20:08:49,195 - server - INFO - get models
2025-01-02 20:12:01,682 - server - INFO - get models
2025-01-02 20:22:29,927 - server - INFO - get models
2025-01-03 08:29:00,335 - server - INFO - get models
2025-01-03 08:49:09,092 - server - INFO - get models
2025-01-03 08:52:08,182 - server - INFO - get models
2025-01-03 08:53:41,169 - server - INFO - get models
2025-01-03 08:53:45,524 - server - INFO - get models
2025-01-03 08:53:50,267 - server - INFO - get models
2025-01-03 08:53:54,672 - server - INFO - get models
2025-01-03 08:53:55,262 - server - INFO - get models
2025-01-03 08:53:58,279 - server - INFO - get models
2025-01-03 08:54:57,484 - server - INFO - get models
2025-01-03 08:55:35,732 - server - INFO - get models
2025-01-03 08:55:37,437 - server - INFO - get models
2025-01-03 08:56:05,424 - server - INFO - get models
2025-01-03 08:56:07,371 - server - INFO - get models
2025-01-03 09:02:10,141 - server - INFO - get models
2025-01-03 09:03:59,324 - server - INFO - get models
2025-01-03 09:04:47,981 - server - INFO - get models
2025-01-03 09:37:42,138 - server - INFO - get models
2025-01-03 09:42:35,501 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市18\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-01-03 09:42:35,503 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-01-03 09:42:35,504 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-01-03 09:43:25,261 - server - INFO - get models
2025-01-03 09:49:12,215 - server - INFO - get models
2025-01-03 09:49:22,369 - server - INFO - get models
2025-01-03 09:51:49,721 - server - INFO - get models
2025-01-03 09:53:33,824 - server - INFO - get models
2025-01-03 09:54:42,415 - server - INFO - get models
2025-01-03 09:57:42,132 - server - INFO - get models
2025-01-03 10:02:15,142 - server - INFO - get models
2025-01-03 10:02:45,449 - server - INFO - get models
2025-01-03 10:02:50,392 - server - INFO - get models
2025-01-03 10:02:54,350 - server - INFO - get models
2025-01-03 10:09:26,922 - server - INFO - get models
2025-01-03 10:09:46,512 - server - INFO - get models
2025-01-03 10:09:51,222 - server - INFO - get models
2025-01-03 10:10:47,732 - server - INFO - get models
2025-01-03 10:10:52,392 - server - INFO - get models
2025-01-03 10:11:33,253 - server - INFO - get models
2025-01-03 10:13:03,874 - server - INFO - get models
2025-01-03 10:13:59,115 - server - INFO - get models
2025-01-03 10:16:47,357 - server - INFO - get models
2025-01-03 10:21:37,416 - server - INFO - get models
2025-01-03 10:21:41,481 - server - INFO - get models
2025-01-03 10:22:00,384 - server - INFO - get models
2025-01-03 10:22:03,919 - server - INFO - get models
2025-01-03 10:22:06,163 - server - INFO - get models
2025-01-03 10:22:34,311 - server - INFO - get models
2025-01-03 10:22:55,857 - server - INFO - get models
2025-01-03 10:22:59,808 - server - INFO - get models
2025-01-03 10:25:12,757 - server - INFO - get models
2025-01-03 10:25:55,213 - server - INFO - get models
2025-01-03 10:26:10,965 - server - INFO - get models
2025-01-03 10:26:16,315 - server - INFO - get models
2025-01-03 10:29:09,190 - server - INFO - get models
2025-01-03 10:29:24,960 - server - INFO - get models
2025-01-03 10:31:56,869 - server - INFO - get models
2025-01-03 10:32:02,933 - server - INFO - get models
2025-01-03 10:32:24,111 - server - INFO - get models
2025-01-03 10:32:37,393 - server - INFO - get models
2025-01-03 10:33:14,658 - server - INFO - get models
2025-01-03 10:33:57,019 - server - INFO - get models
2025-01-03 10:34:00,831 - server - INFO - get models
2025-01-03 10:34:38,315 - server - INFO - get models
2025-01-03 10:36:01,610 - server - INFO - get models
2025-01-03 10:36:03,456 - server - INFO - get models
2025-01-03 10:36:24,759 - server - INFO - get models
2025-01-03 10:36:29,717 - server - INFO - get models
2025-01-03 10:36:31,499 - server - INFO - get models
2025-01-03 10:37:13,950 - server - INFO - get models
2025-01-03 10:38:22,987 - server - INFO - get models
2025-01-03 10:39:57,327 - server - INFO - get models
2025-01-03 10:40:14,377 - server - INFO - get models
2025-01-03 10:42:16,074 - server - INFO - get models
2025-01-03 10:42:19,783 - server - INFO - get models
2025-01-03 10:42:22,090 - server - INFO - get models
2025-01-03 10:43:41,515 - server - INFO - get models
2025-01-03 10:44:47,028 - server - INFO - get models
2025-01-03 10:46:28,833 - server - INFO - get models
2025-01-03 10:47:37,887 - server - INFO - get models
2025-01-03 10:53:11,861 - server - INFO - get models
2025-01-03 10:54:22,453 - server - INFO - get models
2025-01-03 10:54:23,726 - server - INFO - get models
2025-01-03 10:54:29,049 - server - INFO - get models
2025-01-03 10:57:02,700 - server - INFO - get models
2025-01-03 10:57:54,818 - server - INFO - get models
2025-01-03 10:58:17,656 - server - INFO - get models
2025-01-03 10:59:55,635 - server - INFO - get models
2025-01-03 11:00:55,468 - server - INFO - get models
2025-01-03 11:01:14,469 - server - INFO - get models
2025-01-03 11:02:41,814 - server - INFO - get models
2025-01-03 11:05:35,189 - server - INFO - get models
2025-01-03 11:06:37,642 - server - INFO - get models
2025-01-03 11:07:47,162 - server - INFO - get models
2025-01-03 11:10:07,282 - server - INFO - get models
2025-01-03 11:12:49,891 - server - INFO - get models
2025-01-03 11:13:38,454 - server - INFO - get models
2025-01-03 11:13:39,049 - server - INFO - get models
2025-01-03 11:14:48,086 - server - INFO - get models
2025-01-03 11:15:07,058 - server - INFO - get models
2025-01-03 11:30:47,423 - server - INFO - get models
2025-01-03 11:33:57,220 - server - INFO - get models
2025-01-03 11:34:55,254 - server - INFO - get models
2025-01-03 11:38:03,797 - server - INFO - get models
2025-01-03 11:39:30,439 - server - INFO - get models
2025-01-03 11:39:43,252 - server - INFO - get models
2025-01-03 11:40:04,397 - server - INFO - get models
2025-01-03 11:42:01,579 - server - INFO - get models
2025-01-03 11:47:38,339 - server - INFO - get models
2025-01-03 11:47:49,406 - server - INFO - get models
2025-01-03 11:48:09,968 - server - INFO - get models
2025-01-03 11:48:21,386 - server - INFO - get models
2025-01-03 11:48:25,894 - server - INFO - get models
2025-01-03 11:48:42,396 - server - INFO - get models
2025-01-03 11:50:50,500 - server - INFO - get models
2025-01-03 11:52:50,459 - server - INFO - get models
2025-01-03 11:58:23,489 - server - INFO - get models
2025-01-03 11:59:00,374 - server - INFO - get models
2025-01-03 11:59:07,243 - server - INFO - get models
2025-01-03 11:59:27,684 - server - INFO - get models
2025-01-03 12:03:59,193 - server - INFO - get models
2025-01-03 12:04:01,948 - server - INFO - get models
2025-01-03 12:09:04,544 - server - INFO - get models
2025-01-03 14:50:44,573 - server - INFO - get models
2025-01-03 14:51:46,181 - server - INFO - get models
2025-01-03 14:52:43,705 - server - INFO - get models
2025-01-03 14:54:28,813 - server - INFO - get models
2025-01-03 14:55:41,092 - server - INFO - get models
2025-01-03 14:56:08,895 - server - INFO - get models
2025-01-03 15:00:39,221 - server - INFO - get models
2025-01-03 15:01:08,490 - server - INFO - get models
2025-01-03 15:02:12,275 - server - INFO - get models
2025-01-03 15:03:12,740 - server - INFO - get models
2025-01-03 15:03:18,906 - server - INFO - get models
2025-01-03 15:03:54,750 - server - INFO - get models
2025-01-03 15:03:59,793 - server - INFO - get models
2025-01-03 15:04:30,126 - server - INFO - get models
2025-01-03 15:05:08,288 - server - INFO - get models
2025-01-03 15:06:44,141 - server - INFO - get models
2025-01-03 15:08:56,501 - server - INFO - get models
2025-01-03 15:27:43,970 - server - INFO - get models
2025-01-03 15:34:40,621 - server - INFO - get models
2025-01-03 15:37:05,139 - server - INFO - get models
2025-01-03 15:39:04,891 - server - INFO - get models
2025-01-03 15:42:44,364 - server - INFO - get models
2025-01-03 15:42:54,528 - server - INFO - get models
2025-01-03 15:43:53,172 - server - INFO - get models
2025-01-03 15:43:58,150 - server - INFO - get models
2025-01-03 15:44:29,413 - server - INFO - get models
2025-01-03 15:44:32,904 - server - INFO - get models
2025-01-03 15:44:50,951 - server - INFO - get models
2025-01-03 15:45:26,582 - server - INFO - get models
2025-01-03 15:45:32,185 - server - INFO - get models
2025-01-03 15:46:14,585 - server - INFO - get models
2025-01-03 15:47:01,852 - server - INFO - get models
2025-01-03 15:48:20,474 - server - INFO - get models
2025-01-03 15:48:24,623 - server - INFO - get models
2025-01-03 15:48:27,477 - server - INFO - get models
2025-01-03 15:48:29,730 - server - INFO - get models
2025-01-03 15:48:37,064 - server - INFO - get models
2025-01-03 15:51:43,583 - server - INFO - get models
2025-01-03 15:52:20,562 - server - INFO - get models
2025-01-03 15:53:18,308 - server - INFO - get models
2025-01-03 15:54:14,616 - server - INFO - get models
2025-01-03 15:55:23,193 - server - INFO - get models
2025-01-03 15:56:17,687 - server - INFO - get models
2025-01-03 15:56:32,947 - server - INFO - get models
2025-01-03 15:57:11,554 - server - INFO - get models
2025-01-03 15:57:14,223 - server - INFO - get models
2025-01-03 15:58:38,923 - server - INFO - get models
2025-01-03 15:58:56,103 - server - INFO - get models
2025-01-03 16:01:42,152 - server - INFO - get models
2025-01-03 16:01:57,597 - server - INFO - get models
2025-01-03 16:02:43,953 - server - INFO - get models
2025-01-03 16:02:49,290 - server - INFO - get models
2025-01-03 16:02:52,860 - server - INFO - get models
2025-01-03 16:06:07,597 - server - INFO - get models
2025-01-03 16:14:29,164 - server - INFO - get models
2025-01-03 16:14:54,823 - server - INFO - get models
2025-01-03 16:24:39,802 - server - INFO - get models
2025-01-03 16:24:46,439 - server - INFO - get models
2025-01-03 16:25:07,880 - server - INFO - get models
2025-01-03 16:25:31,811 - server - INFO - get models
2025-01-03 16:25:50,731 - server - INFO - get models
2025-01-03 16:46:52,784 - server - INFO - get models
2025-01-03 16:56:12,434 - server - INFO - get models
2025-01-03 17:15:58,021 - server - INFO - get models
2025-01-03 17:17:12,499 - server - INFO - get models
2025-01-07 11:15:10,432 - server - INFO - get models
2025-01-07 11:25:54,576 - server - INFO - get models
2025-01-07 11:27:19,274 - server - INFO - get models
2025-01-07 11:30:57,141 - server - INFO - get models
2025-01-07 11:34:19,500 - server - INFO - get models
2025-01-07 11:36:19,776 - server - INFO - get models
2025-01-07 11:38:02,632 - server - INFO - get models
2025-01-07 11:39:59,187 - server - INFO - get models
2025-01-07 11:41:34,755 - server - INFO - get models
2025-01-07 11:50:14,896 - server - INFO - get models
2025-01-07 11:57:53,923 - server - INFO - get models
2025-01-07 11:58:12,077 - server - INFO - get models
2025-01-07 12:00:13,728 - server - INFO - get models
2025-01-07 12:01:44,887 - server - INFO - get models
2025-01-07 12:04:58,049 - server - INFO - get models
2025-01-07 12:05:21,776 - server - INFO - get models
2025-01-07 13:09:23,201 - server - INFO - get models
2025-01-07 13:20:00,931 - server - INFO - get models
2025-01-07 13:21:30,668 - server - INFO - get models
2025-01-07 13:22:47,819 - server - INFO - get models
2025-01-07 13:24:00,815 - server - INFO - get models
2025-01-07 14:02:35,145 - server - INFO - get models
2025-01-07 14:04:44,729 - server - INFO - get models
2025-01-07 14:06:14,984 - server - INFO - get models
2025-01-07 14:07:00,393 - server - INFO - get models
2025-01-07 14:09:53,253 - server - INFO - get models
2025-01-07 14:09:56,490 - server - INFO - get models
2025-01-07 14:10:14,428 - server - INFO - get models
2025-01-07 14:12:35,038 - server - INFO - get models
2025-01-07 14:12:38,076 - server - INFO - get models
2025-01-07 14:13:11,050 - server - INFO - get models
2025-01-07 14:15:36,373 - server - INFO - get models
2025-01-07 14:15:41,276 - server - INFO - get models
2025-01-07 14:15:46,898 - server - INFO - get models
2025-01-07 14:17:18,150 - server - INFO - get models
2025-01-07 14:18:58,308 - server - INFO - get models
2025-01-07 14:19:29,163 - server - INFO - get models
2025-01-07 14:20:53,840 - server - INFO - get models
2025-01-07 14:20:58,473 - server - INFO - get models
2025-01-07 14:21:07,743 - server - INFO - get models
2025-01-07 14:21:13,164 - server - INFO - get models
2025-01-07 14:21:19,795 - server - INFO - get models
2025-01-07 14:21:34,610 - server - INFO - get models
2025-01-07 14:21:43,873 - server - INFO - get models
2025-01-07 14:21:53,826 - server - INFO - get models
2025-01-07 14:22:21,652 - server - INFO - get models
2025-01-07 14:22:25,028 - server - INFO - get models
2025-01-07 14:23:40,525 - server - INFO - get models
2025-01-07 14:23:45,306 - server - INFO - get models
2025-01-07 14:23:49,402 - server - INFO - get models
2025-01-07 14:23:49,869 - server - INFO - get models
2025-01-07 14:24:38,135 - server - INFO - get models
2025-01-07 14:25:07,877 - server - INFO - get models
2025-01-07 14:25:29,741 - server - INFO - get models
2025-01-07 14:30:21,062 - server - INFO - get models
2025-01-07 14:31:39,063 - server - INFO - get models
2025-01-07 14:31:55,417 - server - INFO - get models
2025-01-07 14:32:00,387 - server - INFO - get models
2025-01-07 14:34:13,028 - server - INFO - get models
2025-01-07 14:34:50,095 - server - INFO - get models
2025-01-07 14:35:56,241 - server - INFO - get models
2025-01-07 14:35:59,503 - server - INFO - get models
2025-01-07 14:40:34,249 - server - INFO - get models
2025-01-07 14:40:38,536 - server - INFO - get models
2025-01-07 14:40:42,533 - server - INFO - get models
2025-01-07 14:40:50,785 - server - INFO - get models
2025-01-07 14:42:02,316 - server - INFO - get models
2025-01-07 14:42:31,243 - server - INFO - get models
2025-01-07 14:42:33,589 - server - INFO - get models
2025-01-07 14:42:48,174 - server - INFO - get models
2025-01-07 14:43:03,039 - server - INFO - get models
2025-01-07 14:44:08,822 - server - INFO - get models
2025-01-07 14:44:12,313 - server - INFO - get models
2025-01-07 14:44:38,156 - server - INFO - get models
2025-01-07 14:46:36,696 - server - INFO - get models
2025-01-07 14:46:44,592 - server - INFO - get models
2025-01-07 14:46:46,691 - server - INFO - get models
2025-01-07 14:46:49,967 - server - INFO - get models
2025-01-07 14:48:44,697 - server - INFO - get models
2025-01-07 14:49:45,736 - server - INFO - get models
2025-01-07 14:50:37,780 - server - INFO - get models
2025-01-07 15:01:45,097 - server - INFO - get models
2025-01-07 15:15:57,980 - server - INFO - get models
2025-01-07 15:17:18,036 - server - INFO - get models
2025-01-07 15:17:59,084 - server - INFO - get models
2025-01-07 15:18:12,808 - server - INFO - get models
2025-01-07 15:18:22,001 - server - INFO - get models
2025-01-07 15:18:31,806 - server - INFO - get models
2025-01-07 15:19:38,513 - server - INFO - get models
2025-01-07 15:19:44,548 - server - INFO - get models
2025-01-07 15:19:49,627 - server - INFO - get models
2025-01-07 15:19:55,506 - server - INFO - get models
2025-01-07 15:25:27,183 - server - INFO - get models
2025-01-07 15:25:34,254 - server - INFO - get models
2025-01-07 15:25:38,132 - server - INFO - get models
2025-01-07 15:25:41,806 - server - INFO - get models
2025-01-07 15:25:45,567 - server - INFO - get models
2025-01-07 15:26:31,425 - server - INFO - get models
2025-01-07 15:26:52,994 - server - INFO - get models
2025-01-07 15:27:17,314 - server - INFO - get models
2025-01-07 15:28:18,755 - server - INFO - get models
2025-01-07 15:28:26,406 - server - INFO - get models
2025-01-07 15:28:48,264 - server - INFO - get models
2025-01-07 15:28:54,506 - server - INFO - get models
2025-01-07 15:30:19,443 - server - INFO - get models
2025-01-07 15:31:16,756 - server - INFO - get models
2025-01-07 15:31:27,821 - server - INFO - get models
2025-01-07 15:33:36,781 - server - INFO - get models
2025-01-07 15:33:47,066 - server - INFO - get models
2025-01-07 15:34:03,159 - server - INFO - get models
2025-01-07 15:36:41,475 - server - INFO - get models
2025-01-07 15:36:44,475 - server - INFO - get models
2025-01-07 15:36:47,620 - server - INFO - get models
2025-01-07 15:39:24,499 - server - INFO - get models
2025-01-07 15:39:28,773 - server - INFO - get models
2025-01-07 15:39:32,721 - server - INFO - get models
2025-01-07 15:40:27,676 - server - INFO - get models
2025-01-07 15:40:33,300 - server - INFO - get models
2025-01-07 15:40:38,425 - server - INFO - get models
2025-01-07 15:42:14,975 - server - INFO - get models
2025-01-07 15:42:37,842 - server - INFO - get models
2025-01-07 15:42:49,899 - server - INFO - get models
2025-01-07 15:43:01,440 - server - INFO - get models
2025-01-07 15:43:45,960 - server - INFO - get models
2025-01-07 15:43:54,588 - server - INFO - get models
2025-01-07 15:46:30,378 - server - INFO - get models
2025-01-07 15:46:53,692 - server - INFO - get models
2025-01-07 15:47:09,972 - server - INFO - get models
2025-01-07 15:47:28,184 - server - INFO - get models
2025-01-07 15:48:11,830 - server - INFO - get models
2025-01-07 15:48:18,772 - server - INFO - get models
2025-01-07 15:49:02,414 - server - INFO - get models
2025-01-07 15:49:28,285 - server - INFO - get models
2025-01-07 15:50:01,513 - server - INFO - get models
2025-01-07 16:07:27,459 - server - INFO - get models
2025-01-07 16:07:32,846 - server - INFO - get models
2025-01-07 16:19:56,370 - server - INFO - get models
2025-01-07 17:02:15,885 - server - INFO - get models
2025-01-07 17:03:30,812 - server - INFO - get models
2025-01-07 17:05:59,481 - server - INFO - get models
2025-01-07 17:06:07,100 - server - INFO - get models
2025-01-07 17:06:11,416 - server - INFO - get models
2025-01-07 17:06:14,346 - server - INFO - get models
2025-01-07 17:06:34,232 - server - INFO - get models
2025-01-07 17:07:33,082 - server - INFO - get models
2025-01-07 17:07:46,323 - server - INFO - get models
2025-01-07 17:09:02,207 - server - INFO - get models
2025-01-07 17:09:12,247 - server - INFO - get models
2025-01-07 17:16:38,415 - server - INFO - get models
2025-01-07 17:17:19,116 - server - INFO - get models
2025-01-07 17:17:52,510 - server - INFO - get models
2025-01-07 17:18:37,954 - server - INFO - get models
2025-01-07 17:19:16,073 - server - INFO - get models
2025-01-07 17:20:03,962 - server - INFO - get models
2025-02-27 09:29:03,256 - server - INFO - get models
2025-02-27 09:31:26,764 - server - INFO - get models
2025-02-27 09:32:24,331 - server - INFO - get models
2025-02-27 14:52:59,553 - server - INFO - get models
2025-02-28 08:41:58,983 - server - INFO - get models
2025-02-28 09:35:03,763 - server - INFO - get models
2025-02-28 09:36:15,291 - server - INFO - get models
2025-02-28 09:38:11,359 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-02-28 09:38:11,360 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-02-28 09:38:11,361 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-02-28 09:38:11,598 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2025-02-28 09:38:22,595 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-02-28 09:38:22,597 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-02-28 09:38:22,597 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-02-28 09:38:22,617 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2025-02-28 09:39:58,138 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-02-28 09:39:58,140 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-02-28 09:39:58,140 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-02-28 09:39:58,168 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2025-02-28 09:41:04,098 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-02-28 09:41:04,099 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-02-28 09:41:04,099 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-02-28 09:41:04,118 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2025-02-28 09:49:35,624 - server - INFO - get models
2025-02-28 09:51:06,685 - server - INFO - get models
2025-02-28 09:51:11,693 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-02-28 09:51:11,694 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-02-28 09:51:11,694 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-02-28 09:51:11,722 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2025-02-28 09:51:17,478 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-02-28 09:51:17,479 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-02-28 09:51:17,480 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-02-28 09:51:17,498 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2025-02-28 09:52:03,704 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-02-28 09:52:03,706 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-02-28 09:52:03,706 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-02-28 09:52:03,725 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2025-02-28 10:17:07,035 - server - INFO - get models
2025-02-28 10:20:16,610 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp石家庄市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp石家庄市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp石家庄市\\csv\\result_data\\PolicyDeduction\\exp石家庄市2\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 基础死亡率', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份成年人口占比': 1, '初始当前省份未成年人口占比': 1, '初始当前省份老年人口占比': 1}, 'region': '石家庄市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-02-28 10:20:16,612 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-02-28 10:20:16,621 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '基础死亡率']
2025-02-28 10:20:23,095 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': 'cd', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-02 15:41:04,341 - server - INFO - get models
2025-03-02 15:41:50,212 - server - INFO - get models
2025-03-02 15:42:25,340 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-02 15:42:25,342 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-02 15:42:25,364 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-02 15:42:25,515 - server - ERROR - 发生异常: 系统内部异常，[Errno 2] No such file or directory: 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', Path: /initModel
2025-03-02 21:34:51,387 - server - INFO - get models
2025-03-02 21:34:54,005 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-02 21:34:54,007 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-02 21:34:54,009 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-02 21:34:54,060 - server - ERROR - 发生异常: 系统内部异常，local variable 'tuned_columns' referenced before assignment, Path: /initModel
2025-03-02 21:37:06,543 - server - INFO - get models
2025-03-02 21:37:09,549 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-02 21:37:09,550 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-02 21:37:09,553 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-02 21:37:09,582 - server - ERROR - 发生异常: 系统内部异常，argument of type 'NoneType' is not iterable, Path: /initModel
2025-03-02 21:38:14,213 - server - INFO - get models
2025-03-02 21:38:23,209 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-02 21:38:23,210 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-02 21:38:23,213 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-02 21:38:37,050 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '你好', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-02 21:39:15,937 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['你好', '你好！有什么我可以帮助你的吗？'], 'inputData': '数据中包含哪些指标？', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-02 21:39:25,610 - server - INFO - request: /report, params: {'compare': 0, 'history': ['你好', '你好！有什么我可以帮助你的吗？', '数据中包含哪些指标？', '数据中包含以下指标：\n\n1. 死亡人口\n2. GDP（国内生产总值）\n3. 历史常住人口\n4. 历史出生率\n5. 历史死亡率\n6. 历史平均预期寿命\n\n如果你需要了解更多关于某个指标的信息，请告诉我！'], 'sessionId': 'admin_run_43'}
2025-03-04 20:15:58,593 - server - INFO - get models
2025-03-04 20:16:07,057 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-04 20:16:07,058 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-04 20:16:07,060 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-04 20:16:07,113 - server - ERROR - 发生异常: 系统内部异常，The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all()., Path: /initModel
2025-03-04 20:23:28,889 - server - INFO - get models
2025-03-04 20:23:34,570 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-04 20:23:34,572 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-04 20:23:34,575 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-04 20:23:34,611 - server - ERROR - 发生异常: 系统内部异常，The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all()., Path: /initModel
2025-03-04 20:24:43,131 - server - INFO - get models
2025-03-04 20:24:58,733 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-04 20:24:58,735 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-04 20:24:58,736 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-04 20:26:35,997 - server - ERROR - 发生异常: 系统内部异常，The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all()., Path: /initModel
2025-03-04 20:27:03,534 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-04 20:27:03,537 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-04 20:27:03,538 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-04 20:30:00,296 - server - ERROR - 发生异常: 系统内部异常，The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all()., Path: /initModel
2025-03-04 20:35:02,719 - server - INFO - get models
2025-03-04 20:35:06,877 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-04 20:35:06,879 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-04 20:35:06,879 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-04 20:35:35,398 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-r1:70b', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-04 20:35:35,399 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-04 20:35:35,400 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-04 20:35:49,651 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '你好', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-04 20:43:57,469 - server - INFO - get models
2025-03-04 20:44:00,471 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-04 20:44:00,472 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-04 20:44:00,473 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-04 20:44:09,889 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-ai/DeepSeek-R1', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-04 20:44:09,890 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-04 20:44:09,891 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-04 20:44:33,172 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '你好，请对数据进行简单的总结', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-04 20:48:06,294 - server - INFO - get models
2025-03-04 20:48:15,179 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-04 20:48:15,180 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-04 20:48:15,180 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-04 20:48:24,059 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-04 20:48:24,060 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-04 20:48:24,061 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-04 20:48:29,011 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '你好', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-04 20:48:56,828 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['你好', '你好！有什么我可以帮助你的吗？'], 'inputData': '请对数据进行简单的总结', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-04 20:59:28,731 - server - INFO - get models
2025-03-04 20:59:32,370 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-04 20:59:32,371 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-04 20:59:32,372 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 09:27:48,648 - server - INFO - get models
2025-03-05 09:38:31,360 - server - INFO - get models
2025-03-05 09:38:33,694 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 09:38:33,695 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 09:38:33,696 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 09:38:42,672 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-ai/DeepSeek-R1', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 09:38:42,674 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 09:38:42,674 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 09:39:01,155 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-ai/DeepSeek-R1', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 09:39:01,157 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 09:39:01,158 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 09:39:12,565 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '分析当前石家庄人口数据特征', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-05 09:39:25,780 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-r1:70b', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 09:39:25,781 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 09:39:25,781 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 09:39:47,849 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '分析当前石家庄人口数据特征', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-05 09:39:58,109 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 09:39:58,110 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 09:39:58,111 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 09:40:19,816 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '分析当前石家庄市人口数据特征', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-05 09:42:31,273 - server - INFO - request: /report, params: {'compare': 0, 'history': ['分析当前石家庄市人口数据特征', '要分析石家庄市的人口数据特征，我们需要了解该市的人口总数、人口增长率、出生率、死亡率、年龄结构、迁移情况等。以下是一些常见的人口数据特征分析方法：\n\n1. **人口总数**：了解石家庄市的总人口数量，以及在过去几年中的变化趋势。这可以帮助我们识别该市的人口增长或减少的趋势。\n\n2. **人口增长率**：通过计算人口增长率，可以了解该市人口增长的速度。这通常通过年度人口变化百分比来表示。\n\n3. **出生率和死亡率**：分析出生率和死亡率的数据，以评估自然人口增长。这可以揭示该市人口的自然变化情况。\n\n4. **年龄结构**：分析人口的年龄分布，了解该市的年轻人口、劳动年龄人口和老年人口的比例。这对于预测未来的劳动力市场和养老需求非常重要。\n\n5. **迁移情况**：了解石家庄市的净迁移率，包括迁入和迁出的人口数量。这可以反映该市的吸引力和经济活力。\n\n6. **性别比例**：分析男性和女性人口的比例，以识别性别失衡问题。\n\n7. **人口密度**：了解每平方公里的平均人口数量，以评估城市的拥挤程度和资源分配。\n\n要获取准确的分析结果，我们需要具体的石家庄市人口统计数据。如果你有相关数据，可以提供给我，我将为你进行更详细的分析。'], 'sessionId': 'admin_run_43'}
2025-03-05 09:42:42,210 - server - INFO - get models
2025-03-05 09:44:35,250 - server - INFO - request: /report, params: {'compare': 0, 'history': ['分析当前石家庄市人口数据特征', '要分析石家庄市的人口数据特征，我们需要了解该市的人口总数、人口增长率、出生率、死亡率、年龄结构、迁移情况等。以下是一些常见的人口数据特征分析方法：\n\n1. **人口总数**：了解石家庄市的总人口数量，以及在过去几年中的变化趋势。这可以帮助我们识别该市的人口增长或减少的趋势。\n\n2. **人口增长率**：通过计算人口增长率，可以了解该市人口增长的速度。这通常通过年度人口变化百分比来表示。\n\n3. **出生率和死亡率**：分析出生率和死亡率的数据，以评估自然人口增长。这可以揭示该市人口的自然变化情况。\n\n4. **年龄结构**：分析人口的年龄分布，了解该市的年轻人口、劳动年龄人口和老年人口的比例。这对于预测未来的劳动力市场和养老需求非常重要。\n\n5. **迁移情况**：了解石家庄市的净迁移率，包括迁入和迁出的人口数量。这可以反映该市的吸引力和经济活力。\n\n6. **性别比例**：分析男性和女性人口的比例，以识别性别失衡问题。\n\n7. **人口密度**：了解每平方公里的平均人口数量，以评估城市的拥挤程度和资源分配。\n\n要获取准确的分析结果，我们需要具体的石家庄市人口统计数据。如果你有相关数据，可以提供给我，我将为你进行更详细的分析。'], 'sessionId': 'admin_run_43'}
2025-03-05 09:46:09,763 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '常住人口', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 09:46:09,765 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 09:46:09,765 - server - INFO - indicators: ['常住人口']
2025-03-05 09:47:14,750 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '根据当前数据告诉我石家庄人口数据', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-05 09:47:24,023 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市71\\symbol_value.csv', 'indicators': '常住人口', 'knowledgeBase': 'default_kb', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 09:47:24,024 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 09:47:24,025 - server - INFO - indicators: ['常住人口']
2025-03-05 09:47:30,882 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['根据当前数据告诉我石家庄人口数据', '根据我现有的训练数据，我无法提供石家庄市的最新人口数据或预测结果。如果您需要石家庄市的具体人口数据，建议查阅相关的统计年鉴或政府发布的最新统计报告。'], 'inputData': '当前石家庄人口数据', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-05 09:50:47,737 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\PolicyDeduction\\exp雄安新区28\\symbol_value.csv', 'indicators': '死亡人口, GDP, 规划迁入人口, 历史常住人口, 历史出生率, 历史死亡率', 'knowledgeBase': 'default_kb', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'强迁移政策': 1}, 'region': '雄安新区', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 09:50:47,738 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 09:50:47,738 - server - INFO - indicators: ['死亡人口', 'GDP', '规划迁入人口', '历史常住人口', '历史出生率', '历史死亡率']
2025-03-05 09:53:56,717 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '当前保定市人口数据', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-05 09:54:12,434 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\PolicyDeduction\\exp雄安新区28\\symbol_value.csv', 'indicators': '死亡人口, GDP, 规划迁入人口, 历史常住人口, 历史出生率, 历史死亡率', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'强迁移政策': 1}, 'region': '雄安新区', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 09:54:12,435 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 09:54:12,436 - server - INFO - indicators: ['死亡人口', 'GDP', '规划迁入人口', '历史常住人口', '历史出生率', '历史死亡率']
2025-03-05 09:54:44,902 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['当前保定市人口数据', '截至我所掌握的数据，保定市的最新人口数据是根据第七次全国人口普查结果，截至2020年11月1日零时，保定市常住人口为946.4万人。如果需要更为详细和最新的数据，建议查阅保定市统计局或相关政府部门发布的最新统计报告。'], 'inputData': '当前雄安新区的人口数据', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-05 09:56:48,181 - server - INFO - get models
2025-03-05 10:05:49,344 - server - INFO - get models
2025-03-05 10:12:04,042 - server - INFO - get models
2025-03-05 10:12:34,580 - server - INFO - get models
2025-03-05 10:13:06,514 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\PolicyDeduction\\雄安新区1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 规划迁入人口, 历史常住人口, 历史出生率, 历史死亡率', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史平均预期寿命': 1, '历史城镇人口': 1, '历史农村人口': 1}, 'region': '雄安新区', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 10:13:06,515 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 10:13:06,516 - server - INFO - indicators: ['死亡人口', 'GDP', '规划迁入人口', '历史常住人口', '历史出生率', '历史死亡率']
2025-03-05 10:13:10,579 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\PolicyDeduction\\雄安新区1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 规划迁入人口, 历史常住人口, 历史出生率, 历史死亡率', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史平均预期寿命': 1, '历史城镇人口': 1, '历史农村人口': 1}, 'region': '雄安新区', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 10:13:10,580 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 10:13:10,580 - server - INFO - indicators: ['死亡人口', 'GDP', '规划迁入人口', '历史常住人口', '历史出生率', '历史死亡率']
2025-03-05 10:13:31,764 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-05 10:14:57,758 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-05 10:15:40,667 - server - INFO - get models
2025-03-05 10:15:53,599 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 10:15:53,600 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 10:15:53,601 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 10:16:08,363 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-05 10:17:11,140 - server - INFO - get models
2025-03-05 10:17:11,966 - server - INFO - get models
2025-03-05 10:17:15,896 - server - INFO - get models
2025-03-05 10:22:02,292 - server - INFO - get models
2025-03-05 10:22:34,923 - server - INFO - get models
2025-03-05 10:23:42,277 - server - INFO - get models
2025-03-05 10:23:45,796 - server - INFO - get models
2025-03-05 10:24:52,281 - server - INFO - get models
2025-03-05 10:28:15,259 - server - INFO - get models
2025-03-05 10:29:15,997 - server - INFO - get models
2025-03-05 10:32:17,203 - server - INFO - get models
2025-03-05 10:35:30,773 - server - INFO - get models
2025-03-05 10:37:16,415 - server - INFO - get models
2025-03-05 10:37:23,041 - server - INFO - get models
2025-03-05 10:37:46,807 - server - INFO - get models
2025-03-05 10:40:49,509 - server - INFO - get models
2025-03-05 10:41:19,718 - server - INFO - get models
2025-03-05 10:42:05,423 - server - INFO - get models
2025-03-05 10:42:24,159 - server - INFO - get models
2025-03-05 10:43:12,847 - server - INFO - get models
2025-03-05 10:43:58,962 - server - INFO - get models
2025-03-05 10:47:25,881 - server - INFO - get models
2025-03-05 10:49:52,212 - server - INFO - get models
2025-03-05 10:49:56,028 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 10:49:56,029 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 10:49:56,029 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 10:50:42,028 - server - INFO - get models
2025-03-05 10:51:28,679 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 10:51:28,680 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 10:51:28,682 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 10:51:56,573 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-05 10:52:47,447 - server - INFO - get models
2025-03-05 10:52:55,762 - server - INFO - get models
2025-03-05 10:52:59,893 - server - INFO - get models
2025-03-05 10:55:11,235 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 10:55:11,237 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 10:55:11,237 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 10:55:51,026 - server - INFO - get models
2025-03-05 10:57:32,763 - server - INFO - get models
2025-03-05 10:57:36,548 - server - INFO - get models
2025-03-05 10:57:40,860 - server - INFO - get models
2025-03-05 10:58:28,592 - server - INFO - get models
2025-03-05 11:00:16,246 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 11:00:16,247 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 11:00:16,249 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 11:00:23,964 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 11:00:23,965 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 11:00:23,967 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 11:00:40,760 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 11:00:40,761 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 11:00:40,764 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 11:00:50,800 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\PolicyDeduction\\雄安新区1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 规划迁入人口, 历史常住人口, 历史出生率, 历史死亡率', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史平均预期寿命': 1, '历史城镇人口': 1, '历史农村人口': 1}, 'region': '雄安新区', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 11:00:50,801 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 11:00:50,803 - server - INFO - indicators: ['死亡人口', 'GDP', '规划迁入人口', '历史常住人口', '历史出生率', '历史死亡率']
2025-03-05 11:00:56,169 - server - INFO - get models
2025-03-05 11:01:04,255 - server - INFO - get models
2025-03-05 11:01:45,072 - server - INFO - request: /report, params: {'compare': 1, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-05 11:03:41,748 - server - INFO - request: /report, params: {'compare': 1, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-05 11:05:12,171 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\PolicyDeduction\\雄安新区1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 规划迁入人口, 历史常住人口, 历史出生率, 历史死亡率', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-ai/DeepSeek-R1', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史平均预期寿命': 1, '历史城镇人口': 1, '历史农村人口': 1}, 'region': '雄安新区', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 11:05:12,172 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 11:05:12,173 - server - INFO - indicators: ['死亡人口', 'GDP', '规划迁入人口', '历史常住人口', '历史出生率', '历史死亡率']
2025-03-05 11:05:28,151 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\PolicyDeduction\\雄安新区1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 规划迁入人口, 历史常住人口, 历史出生率, 历史死亡率', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-ai/DeepSeek-R1', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史平均预期寿命': 1, '历史城镇人口': 1, '历史农村人口': 1}, 'region': '雄安新区', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 11:05:28,152 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 11:05:28,152 - server - INFO - indicators: ['死亡人口', 'GDP', '规划迁入人口', '历史常住人口', '历史出生率', '历史死亡率']
2025-03-05 11:10:14,231 - server - INFO - get models
2025-03-05 11:12:31,922 - server - INFO - get models
2025-03-05 11:18:41,048 - server - INFO - get models
2025-03-05 11:20:45,648 - server - INFO - get models
2025-03-05 11:26:49,925 - server - INFO - get models
2025-03-05 16:14:26,202 - server - INFO - get models
2025-03-05 16:23:54,581 - server - INFO - get models
2025-03-05 16:48:18,045 - server - INFO - get models
2025-03-05 16:49:12,789 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 16:49:12,790 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 16:49:12,791 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 16:49:54,199 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '分析当前石家庄人口信息', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-05 16:50:28,386 - server - INFO - get models
2025-03-05 16:51:40,207 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 16:51:40,209 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 16:51:40,209 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 16:51:42,907 - server - INFO - request: /report, params: {'compare': 1, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-05 16:52:14,961 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-05 16:53:10,820 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-05 16:54:21,694 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 16:54:21,696 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 16:54:21,696 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 16:54:30,092 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 16:54:30,093 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 16:54:30,094 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 16:54:49,200 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-05 16:56:05,435 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 16:56:05,436 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 16:56:05,437 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 16:56:15,733 - server - INFO - get models
2025-03-05 16:56:17,157 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {}, 'region': '', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 16:56:17,158 - server - ERROR - 发生异常: 参数校验异常，地区不能为空, Path: /initModel
2025-03-05 17:09:59,558 - server - INFO - get models
2025-03-05 17:13:21,217 - server - INFO - get models
2025-03-05 17:13:30,170 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 17:13:30,171 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 17:13:30,172 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 17:13:49,690 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-05 17:13:49,691 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-05 17:13:49,691 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-05 17:14:00,503 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '5678', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-05 17:14:13,230 - server - INFO - request: /report, params: {'compare': 1, 'history': ['5678', 'It seems like you entered "5678" without any context or question. Could you please provide more details or clarify what you would like to know or discuss?'], 'sessionId': 'admin_run_43'}
2025-03-05 17:14:55,857 - server - INFO - get models
2025-03-05 17:21:48,804 - server - INFO - get models
2025-03-05 17:26:08,981 - server - INFO - get models
2025-03-05 17:28:36,034 - server - INFO - get models
2025-03-05 17:29:26,538 - server - INFO - get models
2025-03-05 17:31:46,678 - server - INFO - get models
2025-03-05 17:31:55,618 - server - INFO - get models
2025-03-05 17:32:57,491 - server - INFO - get models
2025-03-05 17:35:06,314 - server - INFO - get models
2025-03-05 17:37:59,277 - server - INFO - get models
2025-03-05 17:39:25,761 - server - INFO - get models
2025-03-05 17:43:34,105 - server - INFO - get models
2025-03-05 17:44:04,730 - server - INFO - get models
2025-03-05 17:45:21,442 - server - INFO - get models
2025-03-05 17:48:00,942 - server - INFO - get models
2025-03-05 17:50:59,426 - server - INFO - get models
2025-03-05 17:51:17,579 - server - INFO - get models
2025-03-05 17:51:54,727 - server - INFO - get models
2025-03-05 20:05:08,567 - server - INFO - get models
2025-03-05 20:05:44,801 - server - INFO - get models
2025-03-05 20:06:04,995 - server - INFO - get models
2025-03-05 20:07:03,256 - server - INFO - get models
2025-03-06 08:41:47,326 - server - INFO - get models
2025-03-06 08:42:39,379 - server - INFO - get models
2025-03-06 08:50:09,128 - server - INFO - get models
2025-03-06 08:51:29,529 - server - INFO - get models
2025-03-06 08:51:54,709 - server - INFO - get models
2025-03-06 08:54:07,826 - server - INFO - get models
2025-03-06 10:27:04,897 - server - INFO - get models
2025-03-06 10:33:53,623 - server - INFO - get models
2025-03-06 10:34:32,123 - server - INFO - get models
2025-03-06 10:36:36,446 - server - INFO - get models
2025-03-06 10:43:14,083 - server - INFO - get models
2025-03-06 10:43:25,445 - server - INFO - get models
2025-03-06 10:43:49,954 - server - INFO - get models
2025-03-06 10:54:15,449 - server - INFO - get models
2025-03-06 10:57:07,796 - server - INFO - get models
2025-03-06 11:16:56,892 - server - INFO - get models
2025-03-06 11:17:07,591 - server - INFO - get models
2025-03-06 11:17:15,161 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:17:15,162 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 11:17:15,162 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 11:17:47,271 - server - INFO - get models
2025-03-06 11:17:51,864 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:17:51,865 - server - ERROR - 发生异常: 参数校验异常，指标项不能为空, Path: /initModel
2025-03-06 11:18:11,116 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:18:11,118 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 11:18:11,118 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 11:18:25,082 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-06 11:18:34,568 - server - INFO - get models
2025-03-06 11:18:38,264 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:18:38,265 - server - ERROR - 发生异常: 参数校验异常，推演参数不能为空, Path: /initModel
2025-03-06 11:18:58,135 - server - INFO - get models
2025-03-06 11:19:06,873 - server - INFO - get models
2025-03-06 11:19:54,776 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-06 11:20:28,807 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:20:28,808 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 11:20:28,809 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 11:22:17,190 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:22:17,191 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 11:22:17,192 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 11:23:31,929 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:23:31,930 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 11:23:31,931 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 11:23:35,976 - server - INFO - get models
2025-03-06 11:26:04,302 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:26:04,303 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 11:26:04,303 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 11:26:20,322 - server - INFO - get models
2025-03-06 11:27:55,003 - server - INFO - get models
2025-03-06 11:29:51,637 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:29:51,639 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 11:29:51,639 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 11:31:37,648 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:31:37,649 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 11:31:37,649 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 11:31:57,885 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:31:57,886 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 11:31:57,886 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 11:32:10,826 - server - INFO - get models
2025-03-06 11:32:18,550 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:32:18,551 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 11:32:18,551 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 11:33:04,770 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:33:04,772 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 11:33:04,776 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 11:36:09,535 - server - INFO - get models
2025-03-06 11:36:12,040 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:36:12,042 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 11:36:12,042 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 11:38:05,316 - server - INFO - get models
2025-03-06 11:38:08,153 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:38:08,154 - server - ERROR - 发生异常: 参数校验异常，推演参数不能为空, Path: /initModel
2025-03-06 11:42:11,334 - server - INFO - get models
2025-03-06 11:42:17,549 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:42:17,551 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 11:42:17,551 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 11:43:13,831 - server - INFO - get models
2025-03-06 11:43:20,190 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:43:20,191 - server - ERROR - 发生异常: 参数校验异常，推演参数不能为空, Path: /initModel
2025-03-06 11:44:18,464 - server - INFO - get models
2025-03-06 11:44:22,301 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:44:22,303 - server - ERROR - 发生异常: 参数校验异常，推演参数不能为空, Path: /initModel
2025-03-06 11:44:37,198 - server - INFO - get models
2025-03-06 11:48:58,584 - server - INFO - get models
2025-03-06 11:49:05,223 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 11:49:05,224 - server - ERROR - 发生异常: 参数校验异常，推演参数不能为空, Path: /initModel
2025-03-06 14:03:31,980 - server - INFO - get models
2025-03-06 14:03:35,322 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 14:03:35,324 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 14:03:35,324 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 14:04:19,426 - server - INFO - get models
2025-03-06 14:04:25,169 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-06 14:04:25,170 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-06 14:04:25,170 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-06 14:05:50,959 - server - INFO - get models
2025-03-06 14:08:53,765 - server - INFO - get models
2025-03-06 14:10:29,150 - server - INFO - get models
2025-03-06 14:15:11,819 - server - INFO - get models
2025-03-06 14:22:25,905 - server - INFO - get models
2025-03-06 14:38:16,943 - server - INFO - get models
2025-03-06 15:14:40,695 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /
2025-03-06 15:14:40,763 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2025-03-06 15:40:14,885 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /
2025-03-06 15:40:14,913 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2025-03-06 15:40:30,032 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /
2025-03-06 15:40:30,114 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2025-03-06 16:20:50,608 - server - INFO - get models
2025-03-07 08:37:22,940 - server - INFO - get models
2025-03-07 08:37:26,007 - server - INFO - get models
2025-03-07 08:39:08,504 - server - INFO - get models
2025-03-07 08:40:43,446 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\PolicyDeduction\\exp雄安新区29\\symbol_value.csv', 'indicators': '死亡人口, GDP, 规划迁入人口, 历史常住人口, 历史出生率, 历史死亡率', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'迁移政策': 1}, 'region': '雄安新区', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-07 08:40:43,448 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-07 08:40:43,448 - server - INFO - indicators: ['死亡人口', 'GDP', '规划迁入人口', '历史常住人口', '历史出生率', '历史死亡率']
2025-03-07 08:40:46,993 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp雄安新区\\csv\\result_data\\PolicyDeduction\\exp雄安新区29\\symbol_value.csv', 'indicators': '死亡人口, GDP, 规划迁入人口, 历史常住人口, 历史出生率, 历史死亡率', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'迁移政策': 1}, 'region': '雄安新区', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-07 08:40:46,994 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-07 08:40:46,995 - server - INFO - indicators: ['死亡人口', 'GDP', '规划迁入人口', '历史常住人口', '历史出生率', '历史死亡率']
2025-03-07 08:40:55,101 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '你是', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-07 08:50:04,313 - server - INFO - get models
2025-03-07 08:50:09,837 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-07 08:50:09,838 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-07 08:50:09,838 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-07 08:50:21,915 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-07 08:51:07,462 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-07 08:51:07,463 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-07 08:51:07,464 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-07 08:52:05,826 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '分析当前石家庄市人口数据特征', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-07 08:52:36,922 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '分析当前石家庄市人口数据特征', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-07 08:52:51,314 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-07 08:52:51,315 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-07 08:52:51,316 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-07 08:54:23,248 - server - INFO - get models
2025-03-07 08:54:31,603 - server - INFO - get models
2025-03-07 09:03:24,402 - server - INFO - get models
2025-03-07 09:05:00,791 - server - INFO - get models
2025-03-07 09:13:29,077 - server - INFO - get models
2025-03-07 09:23:02,654 - server - INFO - get models
2025-03-07 09:23:28,993 - server - INFO - get models
2025-03-07 09:23:38,788 - server - INFO - get models
2025-03-07 09:26:44,019 - server - INFO - get models
2025-03-07 09:39:42,507 - server - INFO - get models
2025-03-07 09:54:35,190 - server - INFO - get models
2025-03-07 09:57:09,289 - server - INFO - get models
2025-03-07 10:09:55,337 - server - INFO - get models
2025-03-07 10:10:18,174 - server - INFO - get models
2025-03-07 10:13:43,830 - server - INFO - get models
2025-03-07 10:18:54,914 - server - INFO - get models
2025-03-07 10:31:46,064 - server - INFO - get models
2025-03-07 10:37:56,501 - server - INFO - get models
2025-03-07 11:05:16,694 - server - INFO - get models
2025-03-07 11:05:18,568 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-07 11:05:18,569 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-07 11:05:18,570 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-07 11:05:52,479 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '你是', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-07 11:06:33,498 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-r1:70b', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-07 11:06:33,500 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-07 11:06:33,502 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-07 11:06:37,230 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-07 11:06:37,231 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-07 11:06:37,233 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-07 11:06:42,055 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '你是', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-07 11:07:21,006 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-r1:70b', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-07 11:07:21,008 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-07 11:07:21,010 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-07 11:07:25,189 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '你是', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-07 11:08:31,789 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-r1:70b', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-07 11:08:31,790 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-07 11:08:31,793 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-07 11:08:55,919 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-07 11:09:25,895 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-07 11:09:25,897 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-07 11:09:25,900 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-07 11:09:29,985 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-07 11:19:08,760 - server - INFO - get models
2025-03-07 11:19:10,057 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市79\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-07 11:19:10,058 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-07 11:19:10,061 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-07 11:20:56,281 - server - ERROR - 发生异常: 系统内部异常，Connection error., Path: /chat
2025-03-07 11:21:45,194 - server - ERROR - 发生异常: 系统内部异常，Connection error., Path: /chat
2025-03-07 11:24:15,712 - server - INFO - get models
2025-03-07 11:26:05,636 - server - INFO - get models
2025-03-07 11:26:13,905 - server - INFO - get models
2025-03-07 11:26:34,232 - server - INFO - get models
2025-03-07 11:27:20,235 - server - INFO - get models
2025-03-07 11:28:08,275 - server - INFO - get models
2025-03-07 11:28:13,577 - server - INFO - get models
2025-03-07 11:28:20,209 - server - INFO - get models
2025-03-07 11:28:59,698 - server - INFO - get models
2025-03-07 11:29:02,843 - server - INFO - get models
2025-03-07 11:33:23,189 - server - INFO - get models
2025-03-07 11:33:43,230 - server - INFO - get models
2025-03-07 11:34:33,198 - server - INFO - get models
2025-03-07 11:34:47,418 - server - INFO - get models
2025-03-07 11:34:53,019 - server - INFO - get models
2025-03-07 11:35:02,432 - server - INFO - get models
2025-03-07 11:35:16,810 - server - INFO - get models
2025-03-07 11:37:28,074 - server - ERROR - 发生异常: 系统内部异常，Request timed out., Path: /chat
2025-03-07 11:37:44,879 - server - INFO - get models
2025-03-07 11:38:03,735 - server - INFO - get models
2025-03-07 11:38:06,065 - server - INFO - get models
2025-03-07 11:38:16,086 - server - INFO - get models
2025-03-07 11:38:58,895 - server - ERROR - 发生异常: 系统内部异常，Request timed out., Path: /report
2025-03-07 11:39:03,274 - server - INFO - get models
2025-03-07 11:39:10,848 - server - INFO - get models
2025-03-07 11:41:04,480 - server - INFO - get models
2025-03-07 11:45:06,399 - server - INFO - get models
2025-03-07 11:45:38,106 - server - INFO - get models
2025-03-07 11:48:43,259 - server - INFO - get models
2025-03-07 11:49:56,949 - server - INFO - get models
2025-03-07 11:51:27,759 - server - INFO - get models
2025-03-07 11:51:45,141 - server - INFO - get models
2025-03-07 11:51:52,219 - server - INFO - get models
2025-03-07 14:19:43,498 - server - INFO - get models
2025-03-07 14:22:48,441 - server - INFO - get models
2025-03-07 15:09:10,434 - server - INFO - get models
2025-03-07 15:10:12,363 - server - INFO - get models
2025-03-07 15:12:51,382 - server - INFO - get models
2025-03-07 15:14:24,956 - server - INFO - get models
2025-03-07 15:15:49,986 - server - INFO - get models
2025-03-07 15:15:51,355 - server - INFO - get models
2025-03-07 15:28:55,829 - server - INFO - get models
2025-03-07 15:29:59,226 - server - INFO - get models
2025-03-07 15:54:43,999 - server - INFO - get models
2025-03-07 16:04:04,982 - server - INFO - get models
2025-03-07 16:06:30,180 - server - INFO - get models
2025-03-07 16:06:46,580 - server - INFO - get models
2025-03-07 16:07:09,781 - server - INFO - get models
2025-03-07 16:07:58,661 - server - INFO - get models
2025-03-07 16:41:30,386 - server - INFO - get models
2025-03-07 16:52:11,041 - server - INFO - get models
2025-03-07 16:52:30,780 - server - INFO - get models
2025-03-07 16:59:49,351 - server - INFO - get models
2025-03-08 14:49:42,453 - server - INFO - get models
2025-03-08 14:49:47,242 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市81\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-08 14:49:47,244 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-08 14:49:47,246 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-09 02:25:55,665 - server - INFO - get models
2025-03-09 02:25:57,768 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市81\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-09 02:25:57,769 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-09 02:25:57,771 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-09 02:26:04,002 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市81\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-09 02:26:04,003 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-09 02:26:04,006 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-09 02:26:23,536 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '请列出数据中有哪些指标', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-09 02:26:50,825 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['请列出数据中有哪些指标', '数据中包含以下指标：\n\n1. 死亡人口\n2. GDP\n3. 历史常住人口\n4. 历史出生率\n5. 历史死亡率\n6. 历史平均预期寿命'], 'inputData': '请问数据对哪个地区多长时间段内的数据进行了推演？', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-10 14:02:18,863 - server - INFO - get models
2025-03-10 15:20:01,724 - server - INFO - get models
2025-03-10 15:21:31,779 - server - INFO - get models
2025-03-10 15:21:35,757 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市81\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-10 15:21:35,758 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-10 15:21:35,759 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-10 15:21:56,551 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-10 15:22:42,795 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '你是', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-10 15:31:14,077 - server - INFO - get models
2025-03-10 15:35:50,915 - server - INFO - get models
2025-03-10 15:37:30,811 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市81\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-10 15:37:30,812 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-10 15:37:30,813 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-10 15:40:20,918 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': 'cs', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-10 15:43:23,306 - server - INFO - get models
2025-03-10 15:45:41,826 - server - INFO - get models
2025-03-10 15:46:46,361 - server - INFO - get models
2025-03-10 15:47:10,924 - server - INFO - get models
2025-03-10 15:47:14,390 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市82\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-10 15:47:14,391 - server - ERROR - 发生异常: 参数校验异常，推演参数不能为空, Path: /initModel
2025-03-10 15:47:22,746 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市82\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-10 15:47:22,747 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-10 15:47:22,748 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-10 15:48:32,735 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市82\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-10 15:48:32,737 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-10 15:48:32,738 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-10 15:48:41,931 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': 'shjui', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-03-11 15:47:13,760 - server - INFO - get models
2025-03-11 15:55:35,326 - server - INFO - get models
2025-03-11 16:02:46,522 - server - INFO - get models
2025-03-11 16:57:09,828 - server - INFO - get models
2025-03-12 15:05:45,146 - server - INFO - get models
2025-03-12 15:16:34,725 - server - INFO - get models
2025-03-12 15:17:03,876 - server - INFO - get models
2025-03-18 22:51:23,152 - server - INFO - get models
2025-03-18 22:51:44,510 - server - INFO - get models
2025-03-18 22:51:47,107 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市83\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-18 22:51:47,108 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-18 22:51:47,109 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-18 22:52:06,015 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-18 22:52:42,649 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-18 22:56:06,938 - server - INFO - get models
2025-03-18 22:56:21,625 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市83\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-18 22:56:21,627 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-18 22:56:21,628 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-18 22:56:35,053 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-18 23:00:35,003 - server - INFO - get models
2025-03-18 23:00:46,373 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市83\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-03-18 23:00:46,375 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-03-18 23:00:46,376 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-03-18 23:00:59,065 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-03-18 23:01:14,439 - server - INFO - 报告C:\project\main\2025-03-18_23-01-14.md生成完成
2025-03-20 09:11:14,508 - server - INFO - get models
2025-03-20 10:28:26,038 - server - INFO - get models
2025-03-20 17:57:55,409 - server - INFO - get models
2025-03-20 17:58:13,058 - server - INFO - get models
2025-03-20 17:58:54,787 - server - INFO - get models
2025-03-20 18:10:13,157 - server - INFO - get models
2025-03-21 08:33:04,552 - server - INFO - get models
2025-03-21 08:44:00,356 - server - INFO - get models
2025-03-21 08:44:16,256 - server - INFO - get models
2025-03-21 08:44:39,779 - server - INFO - get models
2025-03-21 09:14:47,149 - server - INFO - get models
2025-03-21 09:17:47,489 - server - INFO - get models
2025-03-21 09:17:52,311 - server - INFO - get models
2025-03-21 14:00:16,462 - server - INFO - get models
2025-03-21 14:58:38,287 - server - INFO - get models
2025-03-21 15:06:42,015 - server - INFO - get models
2025-03-21 15:23:05,120 - server - INFO - get models
2025-03-21 18:00:54,038 - server - INFO - get models
2025-03-22 13:11:57,882 - server - INFO - get models
2025-03-26 15:57:00,633 - server - INFO - get models
2025-03-26 16:05:00,788 - server - INFO - get models
2025-03-26 16:06:16,494 - server - INFO - get models
2025-03-27 15:27:00,773 - server - INFO - get models
2025-03-27 15:27:04,430 - server - INFO - get models
2025-03-28 08:48:56,315 - server - INFO - get models
2025-04-03 09:08:47,643 - server - INFO - get models
2025-04-03 09:09:02,942 - server - INFO - get models
2025-04-03 09:10:04,952 - server - INFO - get models
2025-04-09 11:09:31,247 - server - INFO - get models
2025-04-10 16:47:37,170 - server - INFO - get models
2025-04-10 18:04:47,884 - server - INFO - get models
2025-04-10 18:05:29,574 - server - INFO - get models
2025-04-14 14:38:33,441 - server - INFO - get models
2025-04-27 14:36:01,555 - server - INFO - get models
2025-04-27 16:31:50,866 - server - INFO - get models
2025-04-27 16:33:48,657 - server - INFO - get models
2025-04-27 16:53:30,893 - server - INFO - get models
2025-04-27 16:53:42,000 - server - INFO - get models
2025-04-27 16:53:47,540 - server - INFO - get models
2025-04-27 16:54:53,866 - server - INFO - get models
2025-04-27 17:08:18,646 - server - INFO - get models
2025-04-27 17:12:05,958 - server - INFO - get models
2025-04-27 18:55:48,011 - server - INFO - get models
2025-04-27 19:02:27,835 - server - INFO - get models
2025-04-27 19:29:09,014 - server - INFO - get models
2025-04-27 19:31:46,051 - server - INFO - get models
2025-04-28 09:01:45,093 - server - INFO - get models
2025-04-28 09:27:29,582 - server - INFO - get models
2025-04-28 09:37:06,063 - server - INFO - get models
2025-05-08 15:32:05,914 - server - INFO - get models
2025-05-12 17:33:28,511 - server - INFO - get models
2025-05-12 17:33:38,166 - server - INFO - get models
2025-05-12 18:37:22,933 - server - INFO - get models
2025-05-15 16:02:32,627 - server - INFO - get models
2025-05-15 16:09:10,099 - server - INFO - get models
2025-05-21 16:28:49,832 - server - INFO - get models
2025-05-22 09:32:48,127 - server - INFO - get models
2025-05-22 09:43:01,284 - server - INFO - get models
2025-05-22 09:49:23,256 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp石家庄市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp石家庄市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp石家庄市\\csv\\result_data\\PolicyDeduction\\exp石家庄市9\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 人口0岁占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份成年人口占比': 1, '初始当前省份未成年人口占比': 1, '初始当前省份老年人口占比': 1}, 'region': '石家庄市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-05-22 09:49:23,259 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-05-22 09:49:23,259 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '人口0岁占比']
2025-05-22 09:49:31,405 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp石家庄市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp石家庄市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp石家庄市\\csv\\result_data\\PolicyDeduction\\exp石家庄市9\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 人口0岁占比', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '初始当前省份成年人口占比': 1, '初始当前省份未成年人口占比': 1, '初始当前省份老年人口占比': 1}, 'region': '石家庄市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-05-22 09:49:31,406 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-05-22 09:49:31,407 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '人口0岁占比']
2025-05-22 09:49:40,532 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-05-22 09:49:53,853 - server - INFO - 报告C:\project\main\2025-05-22_09-49-53.md生成完成
2025-05-22 09:49:54,299 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-05-22 09:50:43,965 - server - INFO - 报告C:\project\main\2025-05-22_09-50-43.md生成完成
2025-05-22 11:37:03,728 - server - INFO - get models
2025-05-22 11:37:52,811 - server - INFO - get models
2025-05-22 11:39:20,364 - server - INFO - get models
2025-05-22 11:39:31,888 - server - INFO - get models
2025-05-22 11:41:27,042 - server - INFO - get models
2025-05-22 12:15:43,149 - server - INFO - get models
2025-05-25 14:55:58,390 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /
2025-05-25 14:55:59,652 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2025-05-26 08:49:27,689 - server - INFO - get models
2025-05-26 08:49:33,531 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-05-26 08:49:33,533 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-05-26 08:49:33,536 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-05-26 08:49:43,908 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '分析数据', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-05-26 09:12:09,330 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2025-05-26 09:13:27,781 - server - INFO - get models
2025-05-26 09:17:33,995 - server - INFO - get models
2025-05-26 09:17:49,730 - server - INFO - get models
2025-05-26 09:18:00,882 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-05-26 09:18:00,884 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-05-26 09:18:00,884 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-05-26 09:18:04,680 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '1', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-05-26 09:18:25,287 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['1', '请问您有什么问题或需要帮助的地方？'], 'inputData': '如何提升gdp', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-05-26 09:18:57,981 - server - INFO - request: /report, params: {'compare': 0, 'history': ['1', '请问您有什么问题或需要帮助的地方？', '如何提升gdp', '提升GDP是一个复杂的过程，涉及多方面的政策和措施。以下是一些可能的策略：\n\n1. **提高生产率**：通过技术创新、员工培训和优化生产流程来提高生产率，从而增加产出。\n\n2. **增加投资**：吸引国内外投资可以帮助扩大生产能力，改善基础设施，并推动经济增长。\n\n3. **促进消费**：通过增加居民收入和提供消费激励措施来刺激消费需求。\n\n4. **扩大出口**：通过提高产品质量、降低生产成本和开拓国际市场来增加出口。\n\n5. **发展新兴产业**：支持高科技产业、绿色能源和其他新兴产业的发展，以创造新的经济增长点。\n\n6. **改善营商环境**：简化行政审批，降低企业运营成本，提供税收优惠等措施来吸引企业和投资。\n\n7. **加强教育和培训**：提高劳动力的技能水平，以适应不断变化的经济需求。\n\n8. **推动区域发展**：通过政策支持和投资，促进欠发达地区的发展，缩小区域差距。\n\n9. **创新金融政策**：通过适度的货币政策和财政政策来刺激经济活动。\n\n10. **鼓励创业**：提供创业支持和资金，激励创新和新企业的成立。\n\n这些策略需要根据具体国家或地区的经济状况和发展目标来制定和实施。'], 'sessionId': 'admin_run_43'}
2025-05-26 09:19:07,672 - server - INFO - 报告C:\project\main\2025-05-26_09-19-07.md生成完成
2025-05-26 09:19:08,137 - server - INFO - request: /report, params: {'compare': 0, 'history': ['1', '请问您有什么问题或需要帮助的地方？', '如何提升gdp', '提升GDP是一个复杂的过程，涉及多方面的政策和措施。以下是一些可能的策略：\n\n1. **提高生产率**：通过技术创新、员工培训和优化生产流程来提高生产率，从而增加产出。\n\n2. **增加投资**：吸引国内外投资可以帮助扩大生产能力，改善基础设施，并推动经济增长。\n\n3. **促进消费**：通过增加居民收入和提供消费激励措施来刺激消费需求。\n\n4. **扩大出口**：通过提高产品质量、降低生产成本和开拓国际市场来增加出口。\n\n5. **发展新兴产业**：支持高科技产业、绿色能源和其他新兴产业的发展，以创造新的经济增长点。\n\n6. **改善营商环境**：简化行政审批，降低企业运营成本，提供税收优惠等措施来吸引企业和投资。\n\n7. **加强教育和培训**：提高劳动力的技能水平，以适应不断变化的经济需求。\n\n8. **推动区域发展**：通过政策支持和投资，促进欠发达地区的发展，缩小区域差距。\n\n9. **创新金融政策**：通过适度的货币政策和财政政策来刺激经济活动。\n\n10. **鼓励创业**：提供创业支持和资金，激励创新和新企业的成立。\n\n这些策略需要根据具体国家或地区的经济状况和发展目标来制定和实施。'], 'sessionId': 'admin_run_43'}
2025-05-26 09:19:19,183 - server - INFO - 报告C:\project\main\2025-05-26_09-19-19.md生成完成
2025-05-26 15:12:54,461 - server - INFO - get models
2025-05-26 15:13:06,221 - server - INFO - get models
2025-05-26 17:28:59,280 - server - INFO - get models
2025-05-26 17:29:01,523 - server - INFO - get models
2025-05-29 11:27:10,795 - server - INFO - get models
2025-05-29 11:27:41,302 - server - INFO - get models
2025-05-29 14:54:02,932 - server - INFO - get models
2025-05-29 15:00:39,122 - server - INFO - get models
2025-05-29 15:30:53,503 - server - INFO - get models
2025-05-30 14:33:26,091 - server - INFO - get models
2025-06-03 10:24:53,059 - server - INFO - get models
2025-06-03 10:25:52,029 - server - INFO - get models
2025-06-03 10:26:43,196 - server - INFO - get models
2025-06-03 10:28:32,257 - server - INFO - get models
2025-06-03 10:28:40,997 - server - INFO - get models
2025-06-03 10:28:44,490 - server - INFO - get models
2025-06-03 10:37:38,265 - server - INFO - get models
2025-06-03 10:37:42,095 - server - INFO - get models
2025-06-03 10:37:50,503 - server - INFO - get models
2025-06-03 14:25:15,310 - server - INFO - get models
2025-06-03 14:25:17,815 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-03 14:25:17,816 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-03 14:25:17,817 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-03 14:25:23,577 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-03 14:25:34,785 - server - INFO - 报告C:\project\main\2025-06-03_14-25-34.md生成完成
2025-06-03 18:56:23,377 - server - INFO - get models
2025-06-03 18:56:25,030 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-03 18:56:25,032 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-03 18:56:25,033 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-03 18:56:31,949 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-03 18:56:52,946 - server - INFO - 报告C:\project\main\2025-06-03_18-56-52.md生成完成
2025-06-03 18:57:51,607 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-03 18:57:51,609 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-03 18:57:51,609 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-03 18:58:09,063 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-03 18:58:09,064 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-03 18:58:09,064 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-03 18:58:12,272 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-03 18:58:31,403 - server - INFO - 报告C:\project\main\2025-06-03_18-58-31.md生成完成
2025-06-03 18:59:13,980 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-03 18:59:13,981 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-03 18:59:13,982 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-03 18:59:15,699 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-03 18:59:15,700 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-03 18:59:15,700 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-04 14:42:11,532 - server - INFO - get models
2025-06-04 14:42:14,872 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-04 14:42:14,873 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-04 14:42:14,874 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-04 14:42:25,110 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-04 14:42:36,103 - server - INFO - 报告C:\project\main\2025-06-04_14-42-36.md生成完成
2025-06-04 14:43:44,887 - server - INFO - get models
2025-06-04 14:43:47,285 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-04 14:43:47,286 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-04 14:43:47,286 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-04 14:43:53,303 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-04 14:44:10,748 - server - INFO - 报告C:\project\main\2025-06-04_14-44-10.md生成完成
2025-06-04 14:45:53,556 - server - INFO - get models
2025-06-04 14:45:55,922 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-04 14:45:55,923 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-04 14:45:55,924 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-04 14:46:08,798 - server - INFO - get models
2025-06-04 14:46:17,496 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-04 14:46:17,497 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-04 14:46:17,497 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-04 14:48:15,338 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-04 14:48:26,509 - server - INFO - 报告C:\project\main\2025-06-04_14-48-26.md生成完成
2025-06-04 15:30:03,979 - server - INFO - get models
2025-06-04 15:30:06,527 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-04 15:30:06,529 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-04 15:30:06,529 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-04 15:30:08,437 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-04 15:30:20,315 - server - INFO - 报告C:\project\main\2025-06-04_15-30-20.md生成完成
2025-06-04 15:57:51,449 - server - INFO - get models
2025-06-04 16:39:27,543 - server - INFO - get models
2025-06-04 16:39:33,405 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-04 16:39:33,406 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-04 16:39:33,407 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-04 16:39:40,369 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-04 16:39:50,065 - server - INFO - 报告C:\project\main\2025-06-04_16-39-50.md生成完成
2025-06-06 11:30:05,435 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2025-06-06 11:31:09,490 - server - INFO - get models
2025-06-06 11:31:19,800 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-06 11:31:19,801 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-06 11:31:19,802 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-06 11:33:52,657 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '请列出数据中有哪些指标', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-06 11:34:22,432 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['请列出数据中有哪些指标', '数据中包含以下指标：\n\n1. 死亡人口\n2. GDP\n3. 历史常住人口\n4. 历史出生率\n5. 历史死亡率\n6. 历史平均预期寿命'], 'inputData': '请列出数据中有哪些指标', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-06 11:34:36,043 - server - INFO - get models
2025-06-06 11:40:46,268 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-06 11:40:46,270 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-06 11:40:46,270 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-06 11:41:18,053 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-06 11:41:18,055 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-06 11:41:18,055 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-06 11:41:22,424 - server - INFO - get models
2025-06-06 11:41:24,431 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-06 11:41:24,433 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-06 11:41:24,434 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-06 11:41:34,621 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '请问数据对哪个地区多长时间段内的数据进行了推演？', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-06 11:45:45,991 - server - INFO - get models
2025-06-06 11:45:48,867 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-06 11:45:48,870 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-06 11:45:48,871 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-06 11:46:17,591 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-06 11:46:25,793 - server - INFO - 报告C:\project\main\2025-06-06_11-46-25.md生成完成
2025-06-06 11:51:27,573 - server - INFO - get models
2025-06-06 11:51:30,321 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-06 11:51:30,322 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-06 11:51:30,323 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-06 11:51:38,984 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': "已知保定市有以下指标项的推演数据：{'死亡人口': {'data': [{'year': 2010, 'data': 7.70248957926756, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 7.615634039256127, 'change': -0.08685554001143281, 'percentage_change': -1.127629438736508}, {'year': 2012, 'data': 7.737462512708334, 'change': 0.12182847345220704, 'percentage_change': 1.599715438323595}, {'year': 2013, 'data': 6.991955317004445, 'change': -0.7455071957038895, 'percentage_change': -9.63503467033846}, {'year': 2014, 'data': 7.048850792897094, 'change': 0.05689547589264876, 'percentage_change': 0.8137276815010372}, {'year': 2015, 'data': 7.097240195969224, 'change': 0.04838940307213058, 'percentage_change': 0.6864864145073274}, {'year': 2016, 'data': 7.228585414848078, 'change': 0.1313452188788542, 'percentage_change': 1.8506520175750825}, {'year': 2017, 'data': 7.179328341725395, 'change': -0.04925707312268379, 'percentage_change': -0.6814206417414107}, {'year': 2018, 'data': 6.376485186788957, 'change': -0.8028431549364372, 'percentage_change': -11.18270563376255}, {'year': 2019, 'data': 6.398586253682872, 'change': 0.022101066893914556, 'percentage_change': 0.3466026540719389}, {'year': 2020, 'data': 6.378532570664044, 'change': -0.02005368301882804, 'percentage_change': -0.31340802833259646}, {'year': 2021, 'data': 6.348277726120894, 'change': -0.030254844543150305, 'percentage_change': -0.4743229607746691}, {'year': 2022, 'data': 8.815977297663352, 'change': 2.4676995715424583, 'percentage_change': 38.87195359126707}, {'year': 2023, 'data': 9.360898715938395, 'change': 0.5449214182750435, 'percentage_change': 6.181066487313588}, {'year': 2024, 'data': 9.853088705798202, 'change': 0.49218998985980633, 'percentage_change': 5.257935213226651}, {'year': 2025, 'data': 10.14039381670326, 'change': 0.2873051109050575, 'percentage_change': 2.915888808917232}, {'year': 2026, 'data': 10.66307193584522, 'change': 0.5226781191419612, 'percentage_change': 5.154416372675839}, {'year': 2027, 'data': 11.02388870635473, 'change': 0.3608167705095102, 'percentage_change': 3.3837975836642395}, {'year': 2028, 'data': 11.50047533104443, 'change': 0.47658662468970014, 'percentage_change': 4.323216946257552}, {'year': 2029, 'data': 11.98887902905823, 'change': 0.48840369801379957, 'percentage_change': 4.246813144282833}, {'year': 2030, 'data': 12.2604183147328, 'change': 0.2715392856745691, 'percentage_change': 2.2649263956740375}, {'year': 2031, 'data': 12.70806277123166, 'change': 0.4476444564988604, 'percentage_change': 3.651135263149594}, {'year': 2032, 'data': 13.08014446931655, 'change': 0.3720816980848909, 'percentage_change': 2.9279183206995514}, {'year': 2033, 'data': 13.36110905066016, 'change': 0.2809645813436088, 'percentage_change': 2.1480235329410657}, {'year': 2034, 'data': 13.71895740379731, 'change': 0.35784835313715035, 'percentage_change': 2.678283305527466}, {'year': 2035, 'data': 14.13189651983246, 'change': 0.412939116035151, 'percentage_change': 3.0099890529644213}, {'year': 2036, 'data': 14.33378370773933, 'change': 0.20188718790686977, 'percentage_change': 1.4285923168454053}, {'year': 2037, 'data': 14.71121561916097, 'change': 0.37743191142164, 'percentage_change': 2.633163155781755}, {'year': 2038, 'data': 14.84070784027156, 'change': 0.12949222111059022, 'percentage_change': 0.8802278782586125}, {'year': 2039, 'data': 15.10154972931096, 'change': 0.26084188903939953, 'percentage_change': 1.7576108353240552}, {'year': 2040, 'data': 15.35676426848499, 'change': 0.25521453917403036, 'percentage_change': 1.6899890656829633}, {'year': 2041, 'data': 15.40441699083353, 'change': 0.047652722348539456, 'percentage_change': 0.3103044464017197}, {'year': 2042, 'data': 15.57370024169107, 'change': 0.16928325085753926, 'percentage_change': 1.0989266971821916}, {'year': 2043, 'data': 15.68543679761172, 'change': 0.11173655592065046, 'percentage_change': 0.7174695427970915}, {'year': 2044, 'data': 15.90679527257142, 'change': 0.2213584749597004, 'percentage_change': 1.4112356437112716}, {'year': 2045, 'data': 15.8596847449447, 'change': -0.047110527626720966, 'percentage_change': -0.29616605242889565}, {'year': 2046, 'data': 16.04459660608096, 'change': 0.18491186113626235, 'percentage_change': 1.1659239392838707}, {'year': 2047, 'data': 16.03067402411842, 'change': -0.013922581962543035, 'percentage_change': -0.0867742723881654}, {'year': 2048, 'data': 16.0380372225472, 'change': 0.007363198428780748, 'percentage_change': 0.04593193285386936}, {'year': 2049, 'data': 16.08335704457811, 'change': 0.045319822030911894, 'percentage_change': 0.2825771096677508}, {'year': 2050, 'data': 16.07732271187995, 'change': -0.0060343326981602274, 'percentage_change': -0.03751911172173145}], 'unit': nan}, 'GDP': {'data': [{'year': 2010, 'data': 2050.3, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 2429.758959557503, 'change': 379.458959557503, 'percentage_change': 18.507484736745987}, {'year': 2012, 'data': 2902.456735005514, 'change': 472.6977754480108, 'percentage_change': 19.45451311491805}, {'year': 2013, 'data': 3226.278077258569, 'change': 323.8213422530548, 'percentage_change': 11.156801696561365}, {'year': 2014, 'data': 3142.531557257881, 'change': -83.74652000068772, 'percentage_change': -2.5957626092741752}, {'year': 2015, 'data': 3268.436798443099, 'change': 125.90524118521807, 'percentage_change': 4.006490909993624}, {'year': 2016, 'data': 3556.380623051009, 'change': 287.94382460790985, 'percentage_change': 8.809833029204366}, {'year': 2017, 'data': 3685.966195633669, 'change': 129.58557258266, 'percentage_change': 3.6437486961530263}, {'year': 2018, 'data': 3824.624920634097, 'change': 138.65872500042815, 'percentage_change': 3.7618013199546114}, {'year': 2019, 'data': 3637.516271364661, 'change': -187.10864926943623, 'percentage_change': -4.892209122520042}, {'year': 2020, 'data': 3819.848022362807, 'change': 182.33175099814616, 'percentage_change': 5.01253430626558}, {'year': 2021, 'data': 3972.108593140952, 'change': 152.26057077814494, 'percentage_change': 3.9860373995707445}, {'year': 2022, 'data': 4412.409891807511, 'change': 440.3012986665585, 'percentage_change': 11.084825309833473}, {'year': 2023, 'data': 4459.640012919429, 'change': 47.23012111191838, 'percentage_change': 1.0703928753221725}, {'year': 2024, 'data': 5050.924206828824, 'change': 591.2841939093951, 'percentage_change': 13.258563296509681}, {'year': 2025, 'data': 4716.426013397375, 'change': -334.4981934314492, 'percentage_change': -6.6225146079050115}, {'year': 2026, 'data': 5262.257808172321, 'change': 545.8317947749465, 'percentage_change': 11.572996019114237}, {'year': 2027, 'data': 5957.338822690072, 'change': 695.0810145177511, 'percentage_change': 13.20879819757758}, {'year': 2028, 'data': 6580.462242896267, 'change': 623.1234202061942, 'percentage_change': 10.459761291952487}, {'year': 2029, 'data': 7280.299311484587, 'change': 699.8370685883201, 'percentage_change': 10.635074600478218}, {'year': 2030, 'data': 6804.68046411768, 'change': -475.6188473669072, 'percentage_change': -6.532957327957988}, {'year': 2031, 'data': 7720.722150167838, 'change': 916.0416860501582, 'percentage_change': 13.461935367584314}, {'year': 2032, 'data': 8598.144722319053, 'change': 877.4225721512157, 'percentage_change': 11.36451429134957}, {'year': 2033, 'data': 9753.749902341056, 'change': 1155.605180022003, 'percentage_change': 13.4401689822955}, {'year': 2034, 'data': 10911.40075190269, 'change': 1157.6508495616345, 'percentage_change': 11.868777251339813}, {'year': 2035, 'data': 11242.17285249904, 'change': 330.77210059634854, 'percentage_change': 3.031435725964603}, {'year': 2036, 'data': 11581.44191871947, 'change': 339.26906622043134, 'percentage_change': 3.0178246738575516}, {'year': 2037, 'data': 13122.20548564959, 'change': 1540.7635669301199, 'percentage_change': 13.303728307264853}, {'year': 2038, 'data': 12264.73723328713, 'change': -857.4682523624615, 'percentage_change': -6.534482738440474}, {'year': 2039, 'data': 13699.45569443864, 'change': 1434.7184611515113, 'percentage_change': 11.69791438545957}, {'year': 2040, 'data': 15545.84503508838, 'change': 1846.389340649739, 'percentage_change': 13.47782993596811}, {'year': 2041, 'data': 17191.84675357262, 'change': 1646.00171848424, 'percentage_change': 10.588049184647506}, {'year': 2042, 'data': 19051.34893972047, 'change': 1859.502186147849, 'percentage_change': 10.816186374866492}, {'year': 2043, 'data': 17806.9240683448, 'change': -1244.4248713756679, 'percentage_change': -6.531951492322658}, {'year': 2044, 'data': 20209.80023754746, 'change': 2402.87616920266, 'percentage_change': 13.494055233684241}, {'year': 2045, 'data': 22520.15357330427, 'change': 2310.3533357568085, 'percentage_change': 11.431846473496757}, {'year': 2046, 'data': 25539.0176791422, 'change': 3018.8641058379326, 'percentage_change': 13.405166603377605}, {'year': 2047, 'data': 28585.34061081946, 'change': 3046.3229316772595, 'percentage_change': 11.928113171577468}, {'year': 2048, 'data': 29472.81071142246, 'change': 887.4701006029973, 'percentage_change': 3.1046336396183873}, {'year': 2049, 'data': 30386.84898253363, 'change': 914.0382711111706, 'percentage_change': 3.1012931886979027}, {'year': 2050, 'data': 34447.20793425944, 'change': 4060.358951725808, 'percentage_change': 13.362224408525226}], 'unit': '亿元'}, '历史常住人口': {'data': [{'year': 2010, 'data': 1120.8113, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 1127.23, 'change': 6.418699999999944, 'percentage_change': 0.5726833767646653}, {'year': 2012, 'data': 1135.14, 'change': 7.910000000000082, 'percentage_change': 0.7017201458442449}, {'year': 2013, 'data': 1022.93, 'change': -112.21000000000015, 'percentage_change': -9.885124301848244}, {'year': 2014, 'data': 1029.5, 'change': 6.57000000000005, 'percentage_change': 0.6422726872806596}, {'year': 2015, 'data': 1034.9, 'change': 5.400000000000091, 'percentage_change': 0.5245264691597951}, {'year': 2016, 'data': 1042.5, 'change': 7.599999999999909, 'percentage_change': 0.7343704705768584}, {'year': 2017, 'data': 1046.92, 'change': 4.420000000000073, 'percentage_change': 0.42398081534772875}, {'year': 2018, 'data': 935.93, 'change': -110.99000000000012, 'percentage_change': -10.601574141290653}, {'year': 2019, 'data': 939.91, 'change': 3.980000000000018, 'percentage_change': 0.4252454777600908}, {'year': 2020, 'data': 924.261, 'change': -15.649000000000001, 'percentage_change': -1.6649466438276008}, {'year': 2021, 'data': 919.5, 'change': -4.760999999999967, 'percentage_change': -0.5151142372122125}, {'year': 2022, 'data': 914.4, 'change': -5.100000000000023, 'percentage_change': -0.5546492659053859}, {'year': 2023, 'data': 909.89, 'change': -4.509999999999991, 'percentage_change': -0.4932195975503052}, {'year': 2024, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}], 'unit': '万人'}, '历史出生率': {'data': [{'year': 2010, 'data': 1.367, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 1.398, 'change': 0.030999999999999917, 'percentage_change': 2.2677395757132346}, {'year': 2012, 'data': 1.327, 'change': -0.07099999999999995, 'percentage_change': -5.078683834048638}, {'year': 2013, 'data': 1.353, 'change': 0.026000000000000023, 'percentage_change': 1.9593067068575751}, {'year': 2014, 'data': 1.389, 'change': 0.03600000000000003, 'percentage_change': 2.660753880266078}, {'year': 2015, 'data': 1.091, 'change': -0.29800000000000004, 'percentage_change': -21.454283657307418}, {'year': 2016, 'data': 1.222, 'change': 0.131, 'percentage_change': 12.007332722273144}, {'year': 2017, 'data': 1.335, 'change': 0.11299999999999999, 'percentage_change': 9.247135842880523}, {'year': 2018, 'data': 1.063, 'change': -0.272, 'percentage_change': -20.374531835205996}, {'year': 2019, 'data': 1.023, 'change': -0.040000000000000036, 'percentage_change': -3.762935089369712}, {'year': 2020, 'data': 0.6940000000000001, 'change': -0.32899999999999985, 'percentage_change': -32.160312805474085}, {'year': 2021, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2022, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2023, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2024, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}], 'unit': '百分比'}, '历史死亡率': {'data': [{'year': 2010, 'data': 0.733, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 0.697, 'change': -0.03600000000000003, 'percentage_change': -4.911323328785816}, {'year': 2012, 'data': 0.6940000000000001, 'change': -0.0029999999999998916, 'percentage_change': -0.4304160688665555}, {'year': 2013, 'data': 0.67, 'change': -0.02400000000000002, 'percentage_change': -3.458213256484153}, {'year': 2014, 'data': 0.8039999999999999, 'change': 0.1339999999999999, 'percentage_change': 19.999999999999986}, {'year': 2015, 'data': 0.546, 'change': -0.2579999999999999, 'percentage_change': -32.08955223880596}, {'year': 2016, 'data': 0.63, 'change': 0.08399999999999996, 'percentage_change': 15.384615384615378}, {'year': 2017, 'data': 0.667, 'change': 0.03700000000000003, 'percentage_change': 5.873015873015879}, {'year': 2018, 'data': 0.583, 'change': -0.08400000000000007, 'percentage_change': -12.593703148425798}, {'year': 2019, 'data': 0.671, 'change': 0.08800000000000008, 'percentage_change': 15.094339622641522}, {'year': 2020, 'data': 0.748, 'change': 0.07699999999999996, 'percentage_change': 11.475409836065568}, {'year': 2021, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2022, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2023, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2024, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}], 'unit': '百分比'}, '历史平均预期寿命': {'data': [{'year': 2010, 'data': 77.98, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2012, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2013, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2014, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2015, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2016, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2017, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2018, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2019, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2020, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2021, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2022, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2023, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2024, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}], 'unit': nan}}，根据输入完成报告：\n                标题，政策模拟推演分析报告。\n                背景，写一段文字，内容包括对数据中各个指标项的定义，指标项用于反映什么场景的现状，有什么意义。\n                数据分析，用一段文字详细总结一下各个指标项的数据的变化趋势。\n                总结，提出明确的建议或策略以改进或提升指标的表现。指出具体的行动步骤和预期的效果。\n                请确保报告遵循上述结构和格式要求，以保证内容的清晰度和连贯性。", 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-06 14:03:06,311 - server - INFO - get models
2025-06-06 15:40:07,942 - server - INFO - get models
2025-06-06 15:40:09,955 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-06 15:40:09,957 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-06 15:40:09,957 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-06 15:40:25,784 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-r1:7b', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-06 15:40:25,785 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-06 15:40:25,786 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-06 15:40:29,964 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '1', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-06 15:40:41,828 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'qwen2:7b-instruct', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-06 15:40:41,829 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-06 15:40:41,830 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-06 15:40:48,019 - server - ERROR - 发生异常: 系统内部异常，Request timed out., Path: /chat
2025-06-06 15:40:50,130 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '1', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-06 15:41:07,839 - server - ERROR - 发生异常: 系统内部异常，Request timed out., Path: /chat
2025-06-06 15:41:08,282 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-ai/DeepSeek-R1', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-06 15:41:08,283 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-06 15:41:08,284 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-06 15:41:22,143 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '1', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-06 15:42:04,616 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-r1:70b', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-06 15:42:04,618 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-06 15:42:04,618 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-06 15:42:09,268 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '1', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-06 15:42:26,926 - server - ERROR - 发生异常: 系统内部异常，Request timed out., Path: /chat
2025-06-06 15:43:15,154 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-r1:70b', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-06 15:43:15,155 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-06 15:43:15,157 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-09 11:03:45,626 - server - INFO - get models
2025-06-09 11:03:49,058 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-09 11:03:49,059 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-09 11:03:49,062 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-09 11:04:59,153 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '1', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-09 11:07:56,219 - server - INFO - get models
2025-06-09 11:08:07,214 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-09 11:08:07,215 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-09 11:08:07,219 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-09 11:08:17,177 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '请列出数据中有哪些指标', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-09 11:11:37,910 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['请列出数据中有哪些指标', '数据中包含以下指标：\n\n1. 死亡人口\n2. GDP（国内生产总值）\n3. 历史常住人口\n4. 历史出生率\n5. 历史死亡率\n6. 历史平均预期寿命'], 'inputData': '请问数据对哪个地区多长时间段内的数据进行了推演？', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-09 11:19:04,389 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['请列出数据中有哪些指标', '数据中包含以下指标：\n\n1. 死亡人口\n2. GDP（国内生产总值）\n3. 历史常住人口\n4. 历史出生率\n5. 历史死亡率\n6. 历史平均预期寿命', '请问数据对哪个地区多长时间段内的数据进行了推演？', '数据对中国保定市的2010年至2050年这段时间内的指标进行了推演。'], 'inputData': "已知保定市有以下指标项的推演数据：{'死亡人口': {'data': [{'year': 2010, 'data': 7.70248957926756, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 7.615634039256127, 'change': -0.08685554001143281, 'percentage_change': -1.127629438736508}, {'year': 2012, 'data': 7.737462512708334, 'change': 0.12182847345220704, 'percentage_change': 1.599715438323595}, {'year': 2013, 'data': 6.991955317004445, 'change': -0.7455071957038895, 'percentage_change': -9.63503467033846}, {'year': 2014, 'data': 7.048850792897094, 'change': 0.05689547589264876, 'percentage_change': 0.8137276815010372}, {'year': 2015, 'data': 7.097240195969224, 'change': 0.04838940307213058, 'percentage_change': 0.6864864145073274}, {'year': 2016, 'data': 7.228585414848078, 'change': 0.1313452188788542, 'percentage_change': 1.8506520175750825}, {'year': 2017, 'data': 7.179328341725395, 'change': -0.04925707312268379, 'percentage_change': -0.6814206417414107}, {'year': 2018, 'data': 6.376485186788957, 'change': -0.8028431549364372, 'percentage_change': -11.18270563376255}, {'year': 2019, 'data': 6.398586253682872, 'change': 0.022101066893914556, 'percentage_change': 0.3466026540719389}, {'year': 2020, 'data': 6.378532570664044, 'change': -0.02005368301882804, 'percentage_change': -0.31340802833259646}, {'year': 2021, 'data': 6.348277726120894, 'change': -0.030254844543150305, 'percentage_change': -0.4743229607746691}, {'year': 2022, 'data': 8.815977297663352, 'change': 2.4676995715424583, 'percentage_change': 38.87195359126707}, {'year': 2023, 'data': 9.360898715938395, 'change': 0.5449214182750435, 'percentage_change': 6.181066487313588}, {'year': 2024, 'data': 9.853088705798202, 'change': 0.49218998985980633, 'percentage_change': 5.257935213226651}, {'year': 2025, 'data': 10.14039381670326, 'change': 0.2873051109050575, 'percentage_change': 2.915888808917232}, {'year': 2026, 'data': 10.66307193584522, 'change': 0.5226781191419612, 'percentage_change': 5.154416372675839}, {'year': 2027, 'data': 11.02388870635473, 'change': 0.3608167705095102, 'percentage_change': 3.3837975836642395}, {'year': 2028, 'data': 11.50047533104443, 'change': 0.47658662468970014, 'percentage_change': 4.323216946257552}, {'year': 2029, 'data': 11.98887902905823, 'change': 0.48840369801379957, 'percentage_change': 4.246813144282833}, {'year': 2030, 'data': 12.2604183147328, 'change': 0.2715392856745691, 'percentage_change': 2.2649263956740375}, {'year': 2031, 'data': 12.70806277123166, 'change': 0.4476444564988604, 'percentage_change': 3.651135263149594}, {'year': 2032, 'data': 13.08014446931655, 'change': 0.3720816980848909, 'percentage_change': 2.9279183206995514}, {'year': 2033, 'data': 13.36110905066016, 'change': 0.2809645813436088, 'percentage_change': 2.1480235329410657}, {'year': 2034, 'data': 13.71895740379731, 'change': 0.35784835313715035, 'percentage_change': 2.678283305527466}, {'year': 2035, 'data': 14.13189651983246, 'change': 0.412939116035151, 'percentage_change': 3.0099890529644213}, {'year': 2036, 'data': 14.33378370773933, 'change': 0.20188718790686977, 'percentage_change': 1.4285923168454053}, {'year': 2037, 'data': 14.71121561916097, 'change': 0.37743191142164, 'percentage_change': 2.633163155781755}, {'year': 2038, 'data': 14.84070784027156, 'change': 0.12949222111059022, 'percentage_change': 0.8802278782586125}, {'year': 2039, 'data': 15.10154972931096, 'change': 0.26084188903939953, 'percentage_change': 1.7576108353240552}, {'year': 2040, 'data': 15.35676426848499, 'change': 0.25521453917403036, 'percentage_change': 1.6899890656829633}, {'year': 2041, 'data': 15.40441699083353, 'change': 0.047652722348539456, 'percentage_change': 0.3103044464017197}, {'year': 2042, 'data': 15.57370024169107, 'change': 0.16928325085753926, 'percentage_change': 1.0989266971821916}, {'year': 2043, 'data': 15.68543679761172, 'change': 0.11173655592065046, 'percentage_change': 0.7174695427970915}, {'year': 2044, 'data': 15.90679527257142, 'change': 0.2213584749597004, 'percentage_change': 1.4112356437112716}, {'year': 2045, 'data': 15.8596847449447, 'change': -0.047110527626720966, 'percentage_change': -0.29616605242889565}, {'year': 2046, 'data': 16.04459660608096, 'change': 0.18491186113626235, 'percentage_change': 1.1659239392838707}, {'year': 2047, 'data': 16.03067402411842, 'change': -0.013922581962543035, 'percentage_change': -0.0867742723881654}, {'year': 2048, 'data': 16.0380372225472, 'change': 0.007363198428780748, 'percentage_change': 0.04593193285386936}, {'year': 2049, 'data': 16.08335704457811, 'change': 0.045319822030911894, 'percentage_change': 0.2825771096677508}, {'year': 2050, 'data': 16.07732271187995, 'change': -0.0060343326981602274, 'percentage_change': -0.03751911172173145}], 'unit': nan}, 'GDP': {'data': [{'year': 2010, 'data': 2050.3, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 2429.758959557503, 'change': 379.458959557503, 'percentage_change': 18.507484736745987}, {'year': 2012, 'data': 2902.456735005514, 'change': 472.6977754480108, 'percentage_change': 19.45451311491805}, {'year': 2013, 'data': 3226.278077258569, 'change': 323.8213422530548, 'percentage_change': 11.156801696561365}, {'year': 2014, 'data': 3142.531557257881, 'change': -83.74652000068772, 'percentage_change': -2.5957626092741752}, {'year': 2015, 'data': 3268.436798443099, 'change': 125.90524118521807, 'percentage_change': 4.006490909993624}, {'year': 2016, 'data': 3556.380623051009, 'change': 287.94382460790985, 'percentage_change': 8.809833029204366}, {'year': 2017, 'data': 3685.966195633669, 'change': 129.58557258266, 'percentage_change': 3.6437486961530263}, {'year': 2018, 'data': 3824.624920634097, 'change': 138.65872500042815, 'percentage_change': 3.7618013199546114}, {'year': 2019, 'data': 3637.516271364661, 'change': -187.10864926943623, 'percentage_change': -4.892209122520042}, {'year': 2020, 'data': 3819.848022362807, 'change': 182.33175099814616, 'percentage_change': 5.01253430626558}, {'year': 2021, 'data': 3972.108593140952, 'change': 152.26057077814494, 'percentage_change': 3.9860373995707445}, {'year': 2022, 'data': 4412.409891807511, 'change': 440.3012986665585, 'percentage_change': 11.084825309833473}, {'year': 2023, 'data': 4459.640012919429, 'change': 47.23012111191838, 'percentage_change': 1.0703928753221725}, {'year': 2024, 'data': 5050.924206828824, 'change': 591.2841939093951, 'percentage_change': 13.258563296509681}, {'year': 2025, 'data': 4716.426013397375, 'change': -334.4981934314492, 'percentage_change': -6.6225146079050115}, {'year': 2026, 'data': 5262.257808172321, 'change': 545.8317947749465, 'percentage_change': 11.572996019114237}, {'year': 2027, 'data': 5957.338822690072, 'change': 695.0810145177511, 'percentage_change': 13.20879819757758}, {'year': 2028, 'data': 6580.462242896267, 'change': 623.1234202061942, 'percentage_change': 10.459761291952487}, {'year': 2029, 'data': 7280.299311484587, 'change': 699.8370685883201, 'percentage_change': 10.635074600478218}, {'year': 2030, 'data': 6804.68046411768, 'change': -475.6188473669072, 'percentage_change': -6.532957327957988}, {'year': 2031, 'data': 7720.722150167838, 'change': 916.0416860501582, 'percentage_change': 13.461935367584314}, {'year': 2032, 'data': 8598.144722319053, 'change': 877.4225721512157, 'percentage_change': 11.36451429134957}, {'year': 2033, 'data': 9753.749902341056, 'change': 1155.605180022003, 'percentage_change': 13.4401689822955}, {'year': 2034, 'data': 10911.40075190269, 'change': 1157.6508495616345, 'percentage_change': 11.868777251339813}, {'year': 2035, 'data': 11242.17285249904, 'change': 330.77210059634854, 'percentage_change': 3.031435725964603}, {'year': 2036, 'data': 11581.44191871947, 'change': 339.26906622043134, 'percentage_change': 3.0178246738575516}, {'year': 2037, 'data': 13122.20548564959, 'change': 1540.7635669301199, 'percentage_change': 13.303728307264853}, {'year': 2038, 'data': 12264.73723328713, 'change': -857.4682523624615, 'percentage_change': -6.534482738440474}, {'year': 2039, 'data': 13699.45569443864, 'change': 1434.7184611515113, 'percentage_change': 11.69791438545957}, {'year': 2040, 'data': 15545.84503508838, 'change': 1846.389340649739, 'percentage_change': 13.47782993596811}, {'year': 2041, 'data': 17191.84675357262, 'change': 1646.00171848424, 'percentage_change': 10.588049184647506}, {'year': 2042, 'data': 19051.34893972047, 'change': 1859.502186147849, 'percentage_change': 10.816186374866492}, {'year': 2043, 'data': 17806.9240683448, 'change': -1244.4248713756679, 'percentage_change': -6.531951492322658}, {'year': 2044, 'data': 20209.80023754746, 'change': 2402.87616920266, 'percentage_change': 13.494055233684241}, {'year': 2045, 'data': 22520.15357330427, 'change': 2310.3533357568085, 'percentage_change': 11.431846473496757}, {'year': 2046, 'data': 25539.0176791422, 'change': 3018.8641058379326, 'percentage_change': 13.405166603377605}, {'year': 2047, 'data': 28585.34061081946, 'change': 3046.3229316772595, 'percentage_change': 11.928113171577468}, {'year': 2048, 'data': 29472.81071142246, 'change': 887.4701006029973, 'percentage_change': 3.1046336396183873}, {'year': 2049, 'data': 30386.84898253363, 'change': 914.0382711111706, 'percentage_change': 3.1012931886979027}, {'year': 2050, 'data': 34447.20793425944, 'change': 4060.358951725808, 'percentage_change': 13.362224408525226}], 'unit': '亿元'}, '历史常住人口': {'data': [{'year': 2010, 'data': 1120.8113, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 1127.23, 'change': 6.418699999999944, 'percentage_change': 0.5726833767646653}, {'year': 2012, 'data': 1135.14, 'change': 7.910000000000082, 'percentage_change': 0.7017201458442449}, {'year': 2013, 'data': 1022.93, 'change': -112.21000000000015, 'percentage_change': -9.885124301848244}, {'year': 2014, 'data': 1029.5, 'change': 6.57000000000005, 'percentage_change': 0.6422726872806596}, {'year': 2015, 'data': 1034.9, 'change': 5.400000000000091, 'percentage_change': 0.5245264691597951}, {'year': 2016, 'data': 1042.5, 'change': 7.599999999999909, 'percentage_change': 0.7343704705768584}, {'year': 2017, 'data': 1046.92, 'change': 4.420000000000073, 'percentage_change': 0.42398081534772875}, {'year': 2018, 'data': 935.93, 'change': -110.99000000000012, 'percentage_change': -10.601574141290653}, {'year': 2019, 'data': 939.91, 'change': 3.980000000000018, 'percentage_change': 0.4252454777600908}, {'year': 2020, 'data': 924.261, 'change': -15.649000000000001, 'percentage_change': -1.6649466438276008}, {'year': 2021, 'data': 919.5, 'change': -4.760999999999967, 'percentage_change': -0.5151142372122125}, {'year': 2022, 'data': 914.4, 'change': -5.100000000000023, 'percentage_change': -0.5546492659053859}, {'year': 2023, 'data': 909.89, 'change': -4.509999999999991, 'percentage_change': -0.4932195975503052}, {'year': 2024, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}], 'unit': '万人'}, '历史出生率': {'data': [{'year': 2010, 'data': 1.367, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 1.398, 'change': 0.030999999999999917, 'percentage_change': 2.2677395757132346}, {'year': 2012, 'data': 1.327, 'change': -0.07099999999999995, 'percentage_change': -5.078683834048638}, {'year': 2013, 'data': 1.353, 'change': 0.026000000000000023, 'percentage_change': 1.9593067068575751}, {'year': 2014, 'data': 1.389, 'change': 0.03600000000000003, 'percentage_change': 2.660753880266078}, {'year': 2015, 'data': 1.091, 'change': -0.29800000000000004, 'percentage_change': -21.454283657307418}, {'year': 2016, 'data': 1.222, 'change': 0.131, 'percentage_change': 12.007332722273144}, {'year': 2017, 'data': 1.335, 'change': 0.11299999999999999, 'percentage_change': 9.247135842880523}, {'year': 2018, 'data': 1.063, 'change': -0.272, 'percentage_change': -20.374531835205996}, {'year': 2019, 'data': 1.023, 'change': -0.040000000000000036, 'percentage_change': -3.762935089369712}, {'year': 2020, 'data': 0.6940000000000001, 'change': -0.32899999999999985, 'percentage_change': -32.160312805474085}, {'year': 2021, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2022, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2023, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2024, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}], 'unit': '百分比'}, '历史死亡率': {'data': [{'year': 2010, 'data': 0.733, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 0.697, 'change': -0.03600000000000003, 'percentage_change': -4.911323328785816}, {'year': 2012, 'data': 0.6940000000000001, 'change': -0.0029999999999998916, 'percentage_change': -0.4304160688665555}, {'year': 2013, 'data': 0.67, 'change': -0.02400000000000002, 'percentage_change': -3.458213256484153}, {'year': 2014, 'data': 0.8039999999999999, 'change': 0.1339999999999999, 'percentage_change': 19.999999999999986}, {'year': 2015, 'data': 0.546, 'change': -0.2579999999999999, 'percentage_change': -32.08955223880596}, {'year': 2016, 'data': 0.63, 'change': 0.08399999999999996, 'percentage_change': 15.384615384615378}, {'year': 2017, 'data': 0.667, 'change': 0.03700000000000003, 'percentage_change': 5.873015873015879}, {'year': 2018, 'data': 0.583, 'change': -0.08400000000000007, 'percentage_change': -12.593703148425798}, {'year': 2019, 'data': 0.671, 'change': 0.08800000000000008, 'percentage_change': 15.094339622641522}, {'year': 2020, 'data': 0.748, 'change': 0.07699999999999996, 'percentage_change': 11.475409836065568}, {'year': 2021, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2022, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2023, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2024, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}], 'unit': '百分比'}, '历史平均预期寿命': {'data': [{'year': 2010, 'data': 77.98, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2012, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2013, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2014, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2015, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2016, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2017, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2018, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2019, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2020, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2021, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2022, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2023, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2024, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}], 'unit': nan}}，根据输入完成报告：\n                标题，政策模拟推演分析报告。\n                背景，写一段文字，内容包括对数据中各个指标项的定义，指标项用于反映什么场景的现状，有什么意义。\n                数据分析，用一段文字详细总结一下各个指标项的数据的变化趋势。\n                总结，提出明确的建议或策略以改进或提升指标的表现。指出具体的行动步骤和预期的效果。\n                请确保报告遵循上述结构和格式要求，以保证内容的清晰度和连贯性。", 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-09 11:26:23,327 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['请列出数据中有哪些指标', '数据中包含以下指标：\n\n1. 死亡人口\n2. GDP（国内生产总值）\n3. 历史常住人口\n4. 历史出生率\n5. 历史死亡率\n6. 历史平均预期寿命', '请问数据对哪个地区多长时间段内的数据进行了推演？', '数据对中国保定市的2010年至2050年这段时间内的指标进行了推演。', "已知保定市有以下指标项的推演数据：{'死亡人口': {'data': [{'year': 2010, 'data': 7.70248957926756, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 7.615634039256127, 'change': -0.08685554001143281, 'percentage_change': -1.127629438736508}, {'year': 2012, 'data': 7.737462512708334, 'change': 0.12182847345220704, 'percentage_change': 1.599715438323595}, {'year': 2013, 'data': 6.991955317004445, 'change': -0.7455071957038895, 'percentage_change': -9.63503467033846}, {'year': 2014, 'data': 7.048850792897094, 'change': 0.05689547589264876, 'percentage_change': 0.8137276815010372}, {'year': 2015, 'data': 7.097240195969224, 'change': 0.04838940307213058, 'percentage_change': 0.6864864145073274}, {'year': 2016, 'data': 7.228585414848078, 'change': 0.1313452188788542, 'percentage_change': 1.8506520175750825}, {'year': 2017, 'data': 7.179328341725395, 'change': -0.04925707312268379, 'percentage_change': -0.6814206417414107}, {'year': 2018, 'data': 6.376485186788957, 'change': -0.8028431549364372, 'percentage_change': -11.18270563376255}, {'year': 2019, 'data': 6.398586253682872, 'change': 0.022101066893914556, 'percentage_change': 0.3466026540719389}, {'year': 2020, 'data': 6.378532570664044, 'change': -0.02005368301882804, 'percentage_change': -0.31340802833259646}, {'year': 2021, 'data': 6.348277726120894, 'change': -0.030254844543150305, 'percentage_change': -0.4743229607746691}, {'year': 2022, 'data': 8.815977297663352, 'change': 2.4676995715424583, 'percentage_change': 38.87195359126707}, {'year': 2023, 'data': 9.360898715938395, 'change': 0.5449214182750435, 'percentage_change': 6.181066487313588}, {'year': 2024, 'data': 9.853088705798202, 'change': 0.49218998985980633, 'percentage_change': 5.257935213226651}, {'year': 2025, 'data': 10.14039381670326, 'change': 0.2873051109050575, 'percentage_change': 2.915888808917232}, {'year': 2026, 'data': 10.66307193584522, 'change': 0.5226781191419612, 'percentage_change': 5.154416372675839}, {'year': 2027, 'data': 11.02388870635473, 'change': 0.3608167705095102, 'percentage_change': 3.3837975836642395}, {'year': 2028, 'data': 11.50047533104443, 'change': 0.47658662468970014, 'percentage_change': 4.323216946257552}, {'year': 2029, 'data': 11.98887902905823, 'change': 0.48840369801379957, 'percentage_change': 4.246813144282833}, {'year': 2030, 'data': 12.2604183147328, 'change': 0.2715392856745691, 'percentage_change': 2.2649263956740375}, {'year': 2031, 'data': 12.70806277123166, 'change': 0.4476444564988604, 'percentage_change': 3.651135263149594}, {'year': 2032, 'data': 13.08014446931655, 'change': 0.3720816980848909, 'percentage_change': 2.9279183206995514}, {'year': 2033, 'data': 13.36110905066016, 'change': 0.2809645813436088, 'percentage_change': 2.1480235329410657}, {'year': 2034, 'data': 13.71895740379731, 'change': 0.35784835313715035, 'percentage_change': 2.678283305527466}, {'year': 2035, 'data': 14.13189651983246, 'change': 0.412939116035151, 'percentage_change': 3.0099890529644213}, {'year': 2036, 'data': 14.33378370773933, 'change': 0.20188718790686977, 'percentage_change': 1.4285923168454053}, {'year': 2037, 'data': 14.71121561916097, 'change': 0.37743191142164, 'percentage_change': 2.633163155781755}, {'year': 2038, 'data': 14.84070784027156, 'change': 0.12949222111059022, 'percentage_change': 0.8802278782586125}, {'year': 2039, 'data': 15.10154972931096, 'change': 0.26084188903939953, 'percentage_change': 1.7576108353240552}, {'year': 2040, 'data': 15.35676426848499, 'change': 0.25521453917403036, 'percentage_change': 1.6899890656829633}, {'year': 2041, 'data': 15.40441699083353, 'change': 0.047652722348539456, 'percentage_change': 0.3103044464017197}, {'year': 2042, 'data': 15.57370024169107, 'change': 0.16928325085753926, 'percentage_change': 1.0989266971821916}, {'year': 2043, 'data': 15.68543679761172, 'change': 0.11173655592065046, 'percentage_change': 0.7174695427970915}, {'year': 2044, 'data': 15.90679527257142, 'change': 0.2213584749597004, 'percentage_change': 1.4112356437112716}, {'year': 2045, 'data': 15.8596847449447, 'change': -0.047110527626720966, 'percentage_change': -0.29616605242889565}, {'year': 2046, 'data': 16.04459660608096, 'change': 0.18491186113626235, 'percentage_change': 1.1659239392838707}, {'year': 2047, 'data': 16.03067402411842, 'change': -0.013922581962543035, 'percentage_change': -0.0867742723881654}, {'year': 2048, 'data': 16.0380372225472, 'change': 0.007363198428780748, 'percentage_change': 0.04593193285386936}, {'year': 2049, 'data': 16.08335704457811, 'change': 0.045319822030911894, 'percentage_change': 0.2825771096677508}, {'year': 2050, 'data': 16.07732271187995, 'change': -0.0060343326981602274, 'percentage_change': -0.03751911172173145}], 'unit': nan}, 'GDP': {'data': [{'year': 2010, 'data': 2050.3, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 2429.758959557503, 'change': 379.458959557503, 'percentage_change': 18.507484736745987}, {'year': 2012, 'data': 2902.456735005514, 'change': 472.6977754480108, 'percentage_change': 19.45451311491805}, {'year': 2013, 'data': 3226.278077258569, 'change': 323.8213422530548, 'percentage_change': 11.156801696561365}, {'year': 2014, 'data': 3142.531557257881, 'change': -83.74652000068772, 'percentage_change': -2.5957626092741752}, {'year': 2015, 'data': 3268.436798443099, 'change': 125.90524118521807, 'percentage_change': 4.006490909993624}, {'year': 2016, 'data': 3556.380623051009, 'change': 287.94382460790985, 'percentage_change': 8.809833029204366}, {'year': 2017, 'data': 3685.966195633669, 'change': 129.58557258266, 'percentage_change': 3.6437486961530263}, {'year': 2018, 'data': 3824.624920634097, 'change': 138.65872500042815, 'percentage_change': 3.7618013199546114}, {'year': 2019, 'data': 3637.516271364661, 'change': -187.10864926943623, 'percentage_change': -4.892209122520042}, {'year': 2020, 'data': 3819.848022362807, 'change': 182.33175099814616, 'percentage_change': 5.01253430626558}, {'year': 2021, 'data': 3972.108593140952, 'change': 152.26057077814494, 'percentage_change': 3.9860373995707445}, {'year': 2022, 'data': 4412.409891807511, 'change': 440.3012986665585, 'percentage_change': 11.084825309833473}, {'year': 2023, 'data': 4459.640012919429, 'change': 47.23012111191838, 'percentage_change': 1.0703928753221725}, {'year': 2024, 'data': 5050.924206828824, 'change': 591.2841939093951, 'percentage_change': 13.258563296509681}, {'year': 2025, 'data': 4716.426013397375, 'change': -334.4981934314492, 'percentage_change': -6.6225146079050115}, {'year': 2026, 'data': 5262.257808172321, 'change': 545.8317947749465, 'percentage_change': 11.572996019114237}, {'year': 2027, 'data': 5957.338822690072, 'change': 695.0810145177511, 'percentage_change': 13.20879819757758}, {'year': 2028, 'data': 6580.462242896267, 'change': 623.1234202061942, 'percentage_change': 10.459761291952487}, {'year': 2029, 'data': 7280.299311484587, 'change': 699.8370685883201, 'percentage_change': 10.635074600478218}, {'year': 2030, 'data': 6804.68046411768, 'change': -475.6188473669072, 'percentage_change': -6.532957327957988}, {'year': 2031, 'data': 7720.722150167838, 'change': 916.0416860501582, 'percentage_change': 13.461935367584314}, {'year': 2032, 'data': 8598.144722319053, 'change': 877.4225721512157, 'percentage_change': 11.36451429134957}, {'year': 2033, 'data': 9753.749902341056, 'change': 1155.605180022003, 'percentage_change': 13.4401689822955}, {'year': 2034, 'data': 10911.40075190269, 'change': 1157.6508495616345, 'percentage_change': 11.868777251339813}, {'year': 2035, 'data': 11242.17285249904, 'change': 330.77210059634854, 'percentage_change': 3.031435725964603}, {'year': 2036, 'data': 11581.44191871947, 'change': 339.26906622043134, 'percentage_change': 3.0178246738575516}, {'year': 2037, 'data': 13122.20548564959, 'change': 1540.7635669301199, 'percentage_change': 13.303728307264853}, {'year': 2038, 'data': 12264.73723328713, 'change': -857.4682523624615, 'percentage_change': -6.534482738440474}, {'year': 2039, 'data': 13699.45569443864, 'change': 1434.7184611515113, 'percentage_change': 11.69791438545957}, {'year': 2040, 'data': 15545.84503508838, 'change': 1846.389340649739, 'percentage_change': 13.47782993596811}, {'year': 2041, 'data': 17191.84675357262, 'change': 1646.00171848424, 'percentage_change': 10.588049184647506}, {'year': 2042, 'data': 19051.34893972047, 'change': 1859.502186147849, 'percentage_change': 10.816186374866492}, {'year': 2043, 'data': 17806.9240683448, 'change': -1244.4248713756679, 'percentage_change': -6.531951492322658}, {'year': 2044, 'data': 20209.80023754746, 'change': 2402.87616920266, 'percentage_change': 13.494055233684241}, {'year': 2045, 'data': 22520.15357330427, 'change': 2310.3533357568085, 'percentage_change': 11.431846473496757}, {'year': 2046, 'data': 25539.0176791422, 'change': 3018.8641058379326, 'percentage_change': 13.405166603377605}, {'year': 2047, 'data': 28585.34061081946, 'change': 3046.3229316772595, 'percentage_change': 11.928113171577468}, {'year': 2048, 'data': 29472.81071142246, 'change': 887.4701006029973, 'percentage_change': 3.1046336396183873}, {'year': 2049, 'data': 30386.84898253363, 'change': 914.0382711111706, 'percentage_change': 3.1012931886979027}, {'year': 2050, 'data': 34447.20793425944, 'change': 4060.358951725808, 'percentage_change': 13.362224408525226}], 'unit': '亿元'}, '历史常住人口': {'data': [{'year': 2010, 'data': 1120.8113, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 1127.23, 'change': 6.418699999999944, 'percentage_change': 0.5726833767646653}, {'year': 2012, 'data': 1135.14, 'change': 7.910000000000082, 'percentage_change': 0.7017201458442449}, {'year': 2013, 'data': 1022.93, 'change': -112.21000000000015, 'percentage_change': -9.885124301848244}, {'year': 2014, 'data': 1029.5, 'change': 6.57000000000005, 'percentage_change': 0.6422726872806596}, {'year': 2015, 'data': 1034.9, 'change': 5.400000000000091, 'percentage_change': 0.5245264691597951}, {'year': 2016, 'data': 1042.5, 'change': 7.599999999999909, 'percentage_change': 0.7343704705768584}, {'year': 2017, 'data': 1046.92, 'change': 4.420000000000073, 'percentage_change': 0.42398081534772875}, {'year': 2018, 'data': 935.93, 'change': -110.99000000000012, 'percentage_change': -10.601574141290653}, {'year': 2019, 'data': 939.91, 'change': 3.980000000000018, 'percentage_change': 0.4252454777600908}, {'year': 2020, 'data': 924.261, 'change': -15.649000000000001, 'percentage_change': -1.6649466438276008}, {'year': 2021, 'data': 919.5, 'change': -4.760999999999967, 'percentage_change': -0.5151142372122125}, {'year': 2022, 'data': 914.4, 'change': -5.100000000000023, 'percentage_change': -0.5546492659053859}, {'year': 2023, 'data': 909.89, 'change': -4.509999999999991, 'percentage_change': -0.4932195975503052}, {'year': 2024, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}], 'unit': '万人'}, '历史出生率': {'data': [{'year': 2010, 'data': 1.367, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 1.398, 'change': 0.030999999999999917, 'percentage_change': 2.2677395757132346}, {'year': 2012, 'data': 1.327, 'change': -0.07099999999999995, 'percentage_change': -5.078683834048638}, {'year': 2013, 'data': 1.353, 'change': 0.026000000000000023, 'percentage_change': 1.9593067068575751}, {'year': 2014, 'data': 1.389, 'change': 0.03600000000000003, 'percentage_change': 2.660753880266078}, {'year': 2015, 'data': 1.091, 'change': -0.29800000000000004, 'percentage_change': -21.454283657307418}, {'year': 2016, 'data': 1.222, 'change': 0.131, 'percentage_change': 12.007332722273144}, {'year': 2017, 'data': 1.335, 'change': 0.11299999999999999, 'percentage_change': 9.247135842880523}, {'year': 2018, 'data': 1.063, 'change': -0.272, 'percentage_change': -20.374531835205996}, {'year': 2019, 'data': 1.023, 'change': -0.040000000000000036, 'percentage_change': -3.762935089369712}, {'year': 2020, 'data': 0.6940000000000001, 'change': -0.32899999999999985, 'percentage_change': -32.160312805474085}, {'year': 2021, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2022, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2023, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2024, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}], 'unit': '百分比'}, '历史死亡率': {'data': [{'year': 2010, 'data': 0.733, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 0.697, 'change': -0.03600000000000003, 'percentage_change': -4.911323328785816}, {'year': 2012, 'data': 0.6940000000000001, 'change': -0.0029999999999998916, 'percentage_change': -0.4304160688665555}, {'year': 2013, 'data': 0.67, 'change': -0.02400000000000002, 'percentage_change': -3.458213256484153}, {'year': 2014, 'data': 0.8039999999999999, 'change': 0.1339999999999999, 'percentage_change': 19.999999999999986}, {'year': 2015, 'data': 0.546, 'change': -0.2579999999999999, 'percentage_change': -32.08955223880596}, {'year': 2016, 'data': 0.63, 'change': 0.08399999999999996, 'percentage_change': 15.384615384615378}, {'year': 2017, 'data': 0.667, 'change': 0.03700000000000003, 'percentage_change': 5.873015873015879}, {'year': 2018, 'data': 0.583, 'change': -0.08400000000000007, 'percentage_change': -12.593703148425798}, {'year': 2019, 'data': 0.671, 'change': 0.08800000000000008, 'percentage_change': 15.094339622641522}, {'year': 2020, 'data': 0.748, 'change': 0.07699999999999996, 'percentage_change': 11.475409836065568}, {'year': 2021, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2022, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2023, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2024, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}], 'unit': '百分比'}, '历史平均预期寿命': {'data': [{'year': 2010, 'data': 77.98, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2012, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2013, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2014, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2015, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2016, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2017, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2018, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2019, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2020, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2021, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2022, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2023, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2024, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}], 'unit': nan}}，根据输入完成报告：\n                标题，政策模拟推演分析报告。\n                背景，写一段文字，内容包括对数据中各个指标项的定义，指标项用于反映什么场景的现状，有什么意义。\n                数据分析，用一段文字详细总结一下各个指标项的数据的变化趋势。\n                总结，提出明确的建议或策略以改进或提升指标的表现。指出具体的行动步骤和预期的效果。\n                请确保报告遵循上述结构和格式要求，以保证内容的清晰度和连贯性。", '**政策模拟推演分析报告**\n\n**背景**\n\n本报告基于保定市2010年至2050年的多项指标数据进行分析和推演。指标包括死亡人口、GDP（国内生产总值）、历史常住人口、历史出生率、历史死亡率以及历史平均预期寿命。这些指标用于反映保定市在经济、人口和社会发展等方面的现状和变化趋势。死亡人口指标揭示了该地区的健康和医疗条件；GDP是衡量经济发展的重要指标，反映了该地区的经济活力；历史常住人口、出生率和死亡率则提供了人口结构和动态的洞察；平均预期寿命则是衡量居民生活质量和健康水平的重要指标。这些数据的分析有助于政策制定者了解保定市的社会经济发展状况，为未来的规划和决策提供科学依据。\n\n**数据分析**\n\n在分析中，我们观察到保定市的死亡人口从2010年的7.70万人逐渐增加到2050年的16.08万人，期间经历了多次波动，特别是在2022年出现了显著增长，增幅达到38.87%。这可能与人口老龄化或其他健康相关因素有关。GDP方面，从2010年的2050.3亿元增长到2050年的34447.21亿元，显示出显著的经济增长趋势，尽管在某些年份如2030年和2038年出现了负增长。历史常住人口在2010年达到1120.81万人，但在随后的几年中呈现下降趋势，并在2022年后保持稳定在909.89万人。出生率自2010年以来逐渐下降，尤其是在2015年和2018年出现显著下降，之后保持在较低水平。死亡率在整个推演期间相对稳定，但在某些年份如2014年和2020年有明显波动。平均预期寿命在整个期间保持不变，为77.98岁，表明居民的生活质量和健康水平保持稳定。\n\n**总结**\n\n为了改善和提升这些指标的表现，我们建议采取以下策略：\n\n1. **健康和养老政策**：针对死亡人口的增长，特别是与老龄化相关的健康问题，政府应加强医疗卫生服务和养老保障体系建设，提高医疗服务的可及性和质量。\n\n2. **经济多元化和创新**：为保持GDP的稳定增长，保定市应推动经济多元化，支持创新和技术进步，吸引更多的投资和人才，以增强经济的韧性和竞争力。\n\n3. **人口政策优化**：鉴于常住人口和出生率的下降趋势，政府应制定有吸引力的人口政策，鼓励生育和移民，引导人口合理流动，保持适度的人口增长。\n\n4. **环境与生活质量提升**：尽管平均预期寿命稳定，仍需通过改善环境质量和生活条件，进一步提升居民的生活满意度和健康水平。\n\n通过实施这些策略，保定市有望在未来实现社会经济的全面发展，提升居民的生活质量和幸福感。'], 'inputData': "已知保定市有以下指标项的推演数据：{'死亡人口': {'data': [{'year': 2010, 'data': 7.70248957926756, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 7.615634039256127, 'change': -0.08685554001143281, 'percentage_change': -1.127629438736508}, {'year': 2012, 'data': 7.737462512708334, 'change': 0.12182847345220704, 'percentage_change': 1.599715438323595}, {'year': 2013, 'data': 6.991955317004445, 'change': -0.7455071957038895, 'percentage_change': -9.63503467033846}, {'year': 2014, 'data': 7.048850792897094, 'change': 0.05689547589264876, 'percentage_change': 0.8137276815010372}, {'year': 2015, 'data': 7.097240195969224, 'change': 0.04838940307213058, 'percentage_change': 0.6864864145073274}, {'year': 2016, 'data': 7.228585414848078, 'change': 0.1313452188788542, 'percentage_change': 1.8506520175750825}, {'year': 2017, 'data': 7.179328341725395, 'change': -0.04925707312268379, 'percentage_change': -0.6814206417414107}, {'year': 2018, 'data': 6.376485186788957, 'change': -0.8028431549364372, 'percentage_change': -11.18270563376255}, {'year': 2019, 'data': 6.398586253682872, 'change': 0.022101066893914556, 'percentage_change': 0.3466026540719389}, {'year': 2020, 'data': 6.378532570664044, 'change': -0.02005368301882804, 'percentage_change': -0.31340802833259646}, {'year': 2021, 'data': 6.348277726120894, 'change': -0.030254844543150305, 'percentage_change': -0.4743229607746691}, {'year': 2022, 'data': 8.815977297663352, 'change': 2.4676995715424583, 'percentage_change': 38.87195359126707}, {'year': 2023, 'data': 9.360898715938395, 'change': 0.5449214182750435, 'percentage_change': 6.181066487313588}, {'year': 2024, 'data': 9.853088705798202, 'change': 0.49218998985980633, 'percentage_change': 5.257935213226651}, {'year': 2025, 'data': 10.14039381670326, 'change': 0.2873051109050575, 'percentage_change': 2.915888808917232}, {'year': 2026, 'data': 10.66307193584522, 'change': 0.5226781191419612, 'percentage_change': 5.154416372675839}, {'year': 2027, 'data': 11.02388870635473, 'change': 0.3608167705095102, 'percentage_change': 3.3837975836642395}, {'year': 2028, 'data': 11.50047533104443, 'change': 0.47658662468970014, 'percentage_change': 4.323216946257552}, {'year': 2029, 'data': 11.98887902905823, 'change': 0.48840369801379957, 'percentage_change': 4.246813144282833}, {'year': 2030, 'data': 12.2604183147328, 'change': 0.2715392856745691, 'percentage_change': 2.2649263956740375}, {'year': 2031, 'data': 12.70806277123166, 'change': 0.4476444564988604, 'percentage_change': 3.651135263149594}, {'year': 2032, 'data': 13.08014446931655, 'change': 0.3720816980848909, 'percentage_change': 2.9279183206995514}, {'year': 2033, 'data': 13.36110905066016, 'change': 0.2809645813436088, 'percentage_change': 2.1480235329410657}, {'year': 2034, 'data': 13.71895740379731, 'change': 0.35784835313715035, 'percentage_change': 2.678283305527466}, {'year': 2035, 'data': 14.13189651983246, 'change': 0.412939116035151, 'percentage_change': 3.0099890529644213}, {'year': 2036, 'data': 14.33378370773933, 'change': 0.20188718790686977, 'percentage_change': 1.4285923168454053}, {'year': 2037, 'data': 14.71121561916097, 'change': 0.37743191142164, 'percentage_change': 2.633163155781755}, {'year': 2038, 'data': 14.84070784027156, 'change': 0.12949222111059022, 'percentage_change': 0.8802278782586125}, {'year': 2039, 'data': 15.10154972931096, 'change': 0.26084188903939953, 'percentage_change': 1.7576108353240552}, {'year': 2040, 'data': 15.35676426848499, 'change': 0.25521453917403036, 'percentage_change': 1.6899890656829633}, {'year': 2041, 'data': 15.40441699083353, 'change': 0.047652722348539456, 'percentage_change': 0.3103044464017197}, {'year': 2042, 'data': 15.57370024169107, 'change': 0.16928325085753926, 'percentage_change': 1.0989266971821916}, {'year': 2043, 'data': 15.68543679761172, 'change': 0.11173655592065046, 'percentage_change': 0.7174695427970915}, {'year': 2044, 'data': 15.90679527257142, 'change': 0.2213584749597004, 'percentage_change': 1.4112356437112716}, {'year': 2045, 'data': 15.8596847449447, 'change': -0.047110527626720966, 'percentage_change': -0.29616605242889565}, {'year': 2046, 'data': 16.04459660608096, 'change': 0.18491186113626235, 'percentage_change': 1.1659239392838707}, {'year': 2047, 'data': 16.03067402411842, 'change': -0.013922581962543035, 'percentage_change': -0.0867742723881654}, {'year': 2048, 'data': 16.0380372225472, 'change': 0.007363198428780748, 'percentage_change': 0.04593193285386936}, {'year': 2049, 'data': 16.08335704457811, 'change': 0.045319822030911894, 'percentage_change': 0.2825771096677508}, {'year': 2050, 'data': 16.07732271187995, 'change': -0.0060343326981602274, 'percentage_change': -0.03751911172173145}], 'unit': nan}, 'GDP': {'data': [{'year': 2010, 'data': 2050.3, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 2429.758959557503, 'change': 379.458959557503, 'percentage_change': 18.507484736745987}, {'year': 2012, 'data': 2902.456735005514, 'change': 472.6977754480108, 'percentage_change': 19.45451311491805}, {'year': 2013, 'data': 3226.278077258569, 'change': 323.8213422530548, 'percentage_change': 11.156801696561365}, {'year': 2014, 'data': 3142.531557257881, 'change': -83.74652000068772, 'percentage_change': -2.5957626092741752}, {'year': 2015, 'data': 3268.436798443099, 'change': 125.90524118521807, 'percentage_change': 4.006490909993624}, {'year': 2016, 'data': 3556.380623051009, 'change': 287.94382460790985, 'percentage_change': 8.809833029204366}, {'year': 2017, 'data': 3685.966195633669, 'change': 129.58557258266, 'percentage_change': 3.6437486961530263}, {'year': 2018, 'data': 3824.624920634097, 'change': 138.65872500042815, 'percentage_change': 3.7618013199546114}, {'year': 2019, 'data': 3637.516271364661, 'change': -187.10864926943623, 'percentage_change': -4.892209122520042}, {'year': 2020, 'data': 3819.848022362807, 'change': 182.33175099814616, 'percentage_change': 5.01253430626558}, {'year': 2021, 'data': 3972.108593140952, 'change': 152.26057077814494, 'percentage_change': 3.9860373995707445}, {'year': 2022, 'data': 4412.409891807511, 'change': 440.3012986665585, 'percentage_change': 11.084825309833473}, {'year': 2023, 'data': 4459.640012919429, 'change': 47.23012111191838, 'percentage_change': 1.0703928753221725}, {'year': 2024, 'data': 5050.924206828824, 'change': 591.2841939093951, 'percentage_change': 13.258563296509681}, {'year': 2025, 'data': 4716.426013397375, 'change': -334.4981934314492, 'percentage_change': -6.6225146079050115}, {'year': 2026, 'data': 5262.257808172321, 'change': 545.8317947749465, 'percentage_change': 11.572996019114237}, {'year': 2027, 'data': 5957.338822690072, 'change': 695.0810145177511, 'percentage_change': 13.20879819757758}, {'year': 2028, 'data': 6580.462242896267, 'change': 623.1234202061942, 'percentage_change': 10.459761291952487}, {'year': 2029, 'data': 7280.299311484587, 'change': 699.8370685883201, 'percentage_change': 10.635074600478218}, {'year': 2030, 'data': 6804.68046411768, 'change': -475.6188473669072, 'percentage_change': -6.532957327957988}, {'year': 2031, 'data': 7720.722150167838, 'change': 916.0416860501582, 'percentage_change': 13.461935367584314}, {'year': 2032, 'data': 8598.144722319053, 'change': 877.4225721512157, 'percentage_change': 11.36451429134957}, {'year': 2033, 'data': 9753.749902341056, 'change': 1155.605180022003, 'percentage_change': 13.4401689822955}, {'year': 2034, 'data': 10911.40075190269, 'change': 1157.6508495616345, 'percentage_change': 11.868777251339813}, {'year': 2035, 'data': 11242.17285249904, 'change': 330.77210059634854, 'percentage_change': 3.031435725964603}, {'year': 2036, 'data': 11581.44191871947, 'change': 339.26906622043134, 'percentage_change': 3.0178246738575516}, {'year': 2037, 'data': 13122.20548564959, 'change': 1540.7635669301199, 'percentage_change': 13.303728307264853}, {'year': 2038, 'data': 12264.73723328713, 'change': -857.4682523624615, 'percentage_change': -6.534482738440474}, {'year': 2039, 'data': 13699.45569443864, 'change': 1434.7184611515113, 'percentage_change': 11.69791438545957}, {'year': 2040, 'data': 15545.84503508838, 'change': 1846.389340649739, 'percentage_change': 13.47782993596811}, {'year': 2041, 'data': 17191.84675357262, 'change': 1646.00171848424, 'percentage_change': 10.588049184647506}, {'year': 2042, 'data': 19051.34893972047, 'change': 1859.502186147849, 'percentage_change': 10.816186374866492}, {'year': 2043, 'data': 17806.9240683448, 'change': -1244.4248713756679, 'percentage_change': -6.531951492322658}, {'year': 2044, 'data': 20209.80023754746, 'change': 2402.87616920266, 'percentage_change': 13.494055233684241}, {'year': 2045, 'data': 22520.15357330427, 'change': 2310.3533357568085, 'percentage_change': 11.431846473496757}, {'year': 2046, 'data': 25539.0176791422, 'change': 3018.8641058379326, 'percentage_change': 13.405166603377605}, {'year': 2047, 'data': 28585.34061081946, 'change': 3046.3229316772595, 'percentage_change': 11.928113171577468}, {'year': 2048, 'data': 29472.81071142246, 'change': 887.4701006029973, 'percentage_change': 3.1046336396183873}, {'year': 2049, 'data': 30386.84898253363, 'change': 914.0382711111706, 'percentage_change': 3.1012931886979027}, {'year': 2050, 'data': 34447.20793425944, 'change': 4060.358951725808, 'percentage_change': 13.362224408525226}], 'unit': '亿元'}, '历史常住人口': {'data': [{'year': 2010, 'data': 1120.8113, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 1127.23, 'change': 6.418699999999944, 'percentage_change': 0.5726833767646653}, {'year': 2012, 'data': 1135.14, 'change': 7.910000000000082, 'percentage_change': 0.7017201458442449}, {'year': 2013, 'data': 1022.93, 'change': -112.21000000000015, 'percentage_change': -9.885124301848244}, {'year': 2014, 'data': 1029.5, 'change': 6.57000000000005, 'percentage_change': 0.6422726872806596}, {'year': 2015, 'data': 1034.9, 'change': 5.400000000000091, 'percentage_change': 0.5245264691597951}, {'year': 2016, 'data': 1042.5, 'change': 7.599999999999909, 'percentage_change': 0.7343704705768584}, {'year': 2017, 'data': 1046.92, 'change': 4.420000000000073, 'percentage_change': 0.42398081534772875}, {'year': 2018, 'data': 935.93, 'change': -110.99000000000012, 'percentage_change': -10.601574141290653}, {'year': 2019, 'data': 939.91, 'change': 3.980000000000018, 'percentage_change': 0.4252454777600908}, {'year': 2020, 'data': 924.261, 'change': -15.649000000000001, 'percentage_change': -1.6649466438276008}, {'year': 2021, 'data': 919.5, 'change': -4.760999999999967, 'percentage_change': -0.5151142372122125}, {'year': 2022, 'data': 914.4, 'change': -5.100000000000023, 'percentage_change': -0.5546492659053859}, {'year': 2023, 'data': 909.89, 'change': -4.509999999999991, 'percentage_change': -0.4932195975503052}, {'year': 2024, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 909.89, 'change': 0.0, 'percentage_change': 0.0}], 'unit': '万人'}, '历史出生率': {'data': [{'year': 2010, 'data': 1.367, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 1.398, 'change': 0.030999999999999917, 'percentage_change': 2.2677395757132346}, {'year': 2012, 'data': 1.327, 'change': -0.07099999999999995, 'percentage_change': -5.078683834048638}, {'year': 2013, 'data': 1.353, 'change': 0.026000000000000023, 'percentage_change': 1.9593067068575751}, {'year': 2014, 'data': 1.389, 'change': 0.03600000000000003, 'percentage_change': 2.660753880266078}, {'year': 2015, 'data': 1.091, 'change': -0.29800000000000004, 'percentage_change': -21.454283657307418}, {'year': 2016, 'data': 1.222, 'change': 0.131, 'percentage_change': 12.007332722273144}, {'year': 2017, 'data': 1.335, 'change': 0.11299999999999999, 'percentage_change': 9.247135842880523}, {'year': 2018, 'data': 1.063, 'change': -0.272, 'percentage_change': -20.374531835205996}, {'year': 2019, 'data': 1.023, 'change': -0.040000000000000036, 'percentage_change': -3.762935089369712}, {'year': 2020, 'data': 0.6940000000000001, 'change': -0.32899999999999985, 'percentage_change': -32.160312805474085}, {'year': 2021, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2022, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2023, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2024, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 0.6940000000000001, 'change': 0.0, 'percentage_change': 0.0}], 'unit': '百分比'}, '历史死亡率': {'data': [{'year': 2010, 'data': 0.733, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 0.697, 'change': -0.03600000000000003, 'percentage_change': -4.911323328785816}, {'year': 2012, 'data': 0.6940000000000001, 'change': -0.0029999999999998916, 'percentage_change': -0.4304160688665555}, {'year': 2013, 'data': 0.67, 'change': -0.02400000000000002, 'percentage_change': -3.458213256484153}, {'year': 2014, 'data': 0.8039999999999999, 'change': 0.1339999999999999, 'percentage_change': 19.999999999999986}, {'year': 2015, 'data': 0.546, 'change': -0.2579999999999999, 'percentage_change': -32.08955223880596}, {'year': 2016, 'data': 0.63, 'change': 0.08399999999999996, 'percentage_change': 15.384615384615378}, {'year': 2017, 'data': 0.667, 'change': 0.03700000000000003, 'percentage_change': 5.873015873015879}, {'year': 2018, 'data': 0.583, 'change': -0.08400000000000007, 'percentage_change': -12.593703148425798}, {'year': 2019, 'data': 0.671, 'change': 0.08800000000000008, 'percentage_change': 15.094339622641522}, {'year': 2020, 'data': 0.748, 'change': 0.07699999999999996, 'percentage_change': 11.475409836065568}, {'year': 2021, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2022, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2023, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2024, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 0.748, 'change': 0.0, 'percentage_change': 0.0}], 'unit': '百分比'}, '历史平均预期寿命': {'data': [{'year': 2010, 'data': 77.98, 'change': 0, 'percentage_change': 0}, {'year': 2011, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2012, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2013, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2014, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2015, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2016, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2017, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2018, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2019, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2020, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2021, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2022, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2023, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2024, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2025, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2026, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2027, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2028, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2029, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2030, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2031, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2032, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2033, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2034, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2035, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2036, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2037, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2038, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2039, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2040, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2041, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2042, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2043, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2044, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2045, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2046, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2047, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2048, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2049, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}, {'year': 2050, 'data': 77.98, 'change': 0.0, 'percentage_change': 0.0}], 'unit': nan}}，根据输入完成报告：\n标题，政策模拟推演分析报告。\n背景，写一段文字，内容包括对数据中各个指标项的定义，指标项用于反映什么场景的现状，有什么意义。\n数据分析，用一段文字详细总结一下各个指标项的数据的变化趋势。\n总结，提出明确的建议或策略以改进或提升指标的表现。指出具体的行动步骤和预期的效果。\n请确保报告遵循上述结构和格式要求，以保证内容的清晰度和连贯性。", 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-09 11:27:52,596 - server - INFO - get models
2025-06-09 11:28:05,956 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-09 11:28:05,958 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-09 11:28:05,961 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-09 11:28:17,400 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-09 11:28:26,707 - server - INFO - 报告C:\project\main\2025-06-09_11-28-26.md生成完成
2025-06-09 11:48:03,751 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '写一个报告格式', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-10 15:25:31,890 - server - INFO - get models
2025-06-13 11:32:42,091 - server - INFO - get models
2025-06-13 11:34:06,000 - server - INFO - get models
2025-06-16 10:15:37,991 - server - INFO - get models
2025-06-16 10:15:40,524 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\exp保定市85\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-16 10:15:40,527 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-16 10:15:40,527 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-16 10:16:30,145 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-16 10:16:38,088 - server - INFO - 报告C:\project\main\2025-06-16_10-16-38.md生成完成
2025-06-16 10:17:13,331 - server - INFO - request: /report, params: {'compare': 1, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-16 10:17:22,240 - server - INFO - 报告C:\project\main\2025-06-16_10-17-22.md生成完成
2025-06-16 10:17:59,901 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '死亡人口', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-16 10:18:51,906 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['死亡人口', '保定市的死亡人口数据从2010年到2050年进行了推演。以下是相关信息：\n\n### 原始推演数据（2010-2050年）：\n- **2010年**: 7.7025万人\n- **2011年**: 7.6156万人（下降1.13%）\n- **2012年**: 7.7375万人（增长1.60%）\n- **2013年**: 6.9920万人（下降9.64%）\n- **2014年**: 7.0489万人（增长0.81%）\n- **2015年**: 7.0972万人（增长0.69%）\n- **2016年**: 7.2286万人（增长1.85%）\n- **2017年**: 7.1793万人（下降0.68%）\n- **2018年**: 6.3765万人（下降11.18%）\n- **2019年**: 6.3986万人（增长0.35%）\n- **2020年**: 6.3785万人（下降0.31%）\n- **2021年**: 6.3483万人（下降0.47%）\n- **2022年**: 8.8160万人（增长38.87%）\n- **2023年**: 9.3609万人（增长6.18%）\n- **2024年**: 9.8531万人（增长5.26%）\n- **2025年**: 10.1404万人（增长2.92%）\n- **2026年**: 10.6631万人（增长5.15%）\n- **2027年**: 11.0239万人（增长3.38%）\n- **2028年**: 11.5005万人（增长4.32%）\n- **2029年**: 11.9889万人（增长4.25%）\n- **2030年**: 12.2604万人（增长2.26%）\n- **2031年**: 12.7081万人（增长3.65%）\n- **2032年**: 13.0801万人（增长2.93%）\n- **2033年**: 13.3611万人（增长2.15%）\n- **2034年**: 13.7190万人（增长2.68%）\n- **2035年**: 14.1319万人（增长3.01%）\n- **2036年**: 14.3338万人（增长1.43%）\n- **2037年**: 14.7112万人（增长2.63%）\n- **2038年**: 14.8407万人（增长0.88%）\n- **2039年**: 15.1015万人（增长1.76%）\n- **2040年**: 15.3568万人（增长1.69%）\n- **2041年**: 15.4044万人（增长0.31%）\n- **2042年**: 15.5737万人（增长1.10%）\n- **2043年**: 15.6854万人（增长0.72%）\n- **2044年**: 15.9068万人（增长1.41%）\n- **2045年**: 15.8597万人（下降0.30%）\n- **2046年**: 16.0446万人（增长1.17%）\n- **2047年**: 16.0307万人（下降0.09%）\n- **2048年**: 16.0380万人（增长0.05%）\n- **2049年**: 16.0834万人（增长0.28%）\n- **2050年**: 16.0773万人（下降0.04%）\n\n### 优化后的推演数据（2022-2035年）：\n- **2022年**: 8.9341万人\n- **2023年**: 9.3476万人（增长4.63%）\n- **2024年**: 9.6962万人（增长3.73%）\n- **2025年**: 10.1992万人（增长5.19%）\n- **2026年**: 10.7060万人（增长4.97%）\n- **2027年**: 11.1671万人（增长4.31%）\n- **2028年**: 11.5749万人（增长3.65%）\n- **2029年**: 11.8945万人（增长2.76%）\n- **2030年**: 12.4196万人（增长4.41%）\n- **2031年**: 12.6912万人（增长2.19%）\n- **2032年**: 13.1403万人（增长3.54%）\n- **2033年**: 13.4698万人（增长2.51%）\n- **2034年**: 13.8332万人（增长2.70%）\n- **2035年**: 14.0203万人（增长1.35%）\n\n这些数据反映了保定市在未来几年的死亡人口变化趋势。请注意，这些数据是基于模型预测，实际情况可能会有所不同。'], 'inputData': '出生人口', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-16 10:19:17,323 - server - INFO - get models
2025-06-16 10:20:19,143 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-16 10:20:19,144 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-16 10:20:19,144 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-16 10:20:30,671 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '出生人口', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-16 10:20:45,254 - server - INFO - request: /report, params: {'compare': 0, 'history': ['出生人口', '根据保定市的历史出生率数据，可以计算出生人口。出生人口的计算公式为：\n\n出生人口 = 历史常住人口 * 历史出生率\n\n我们可以利用每年的常住人口和出生率来计算相应年份的出生人口。以下是一些年份的出生人口计算示例：\n\n- 2010年: \n  - 常住人口: 1120.8113万人\n  - 出生率: 1.367%\n  - 出生人口 = 1120.8113 * 1.367 / 100 = 15.31万人\n\n- 2011年:\n  - 常住人口: 1127.23万人\n  - 出生率: 1.398%\n  - 出生人口 = 1127.23 * 1.398 / 100 = 15.76万人\n\n- 2012年:\n  - 常住人口: 1135.14万人\n  - 出生率: 1.327%\n  - 出生人口 = 1135.14 * 1.327 / 100 = 15.06万人\n\n以此类推，可以计算其他年份的出生人口。注意，以上计算结果是基于给定的数据进行的近似计算，实际出生人口可能会受到其他因素影响。'], 'sessionId': 'admin_run_43'}
2025-06-16 10:20:56,677 - server - INFO - 报告C:\project\main\2025-06-16_10-20-56.md生成完成
2025-06-16 11:08:44,070 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2025-06-16 18:52:50,316 - server - INFO - get models
2025-06-16 18:52:52,691 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-16 18:52:52,693 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-16 18:52:52,696 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-16 18:53:08,555 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-16 18:53:21,698 - server - INFO - 报告C:\project\main\2025-06-16_18-53-21.md生成完成
2025-06-16 18:53:27,881 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '你是谁', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-16 18:53:33,169 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['你是谁', '我是OpenAI开发的人工智能助手，旨在帮助回答问题和提供信息。有什么我可以帮助您的呢？'], 'inputData': '1', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-17 14:37:42,882 - server - INFO - get models
2025-06-17 14:37:45,111 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-17 14:37:45,113 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-17 14:37:45,115 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-17 14:37:46,557 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-17 14:37:55,058 - server - INFO - 报告C:\project\main\2025-06-17_14-37-55.md生成完成
2025-06-17 14:37:55,058 - server - ERROR - 发生异常: 系统内部异常，success() takes 2 positional arguments but 3 were given, Path: /report
2025-06-17 18:02:26,911 - server - INFO - get models
2025-06-19 08:43:07,868 - server - INFO - get models
2025-06-19 08:44:40,189 - server - INFO - get models
2025-06-19 08:44:42,379 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-19 08:44:42,380 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-19 08:44:42,381 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-19 08:44:50,197 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-19 08:44:58,644 - server - INFO - 报告C:\project\main\2025-06-19_08-44-58.md生成完成
2025-06-19 08:44:58,645 - server - ERROR - 发生异常: 系统内部异常，success() takes 2 positional arguments but 3 were given, Path: /report
2025-06-19 14:59:41,525 - server - INFO - get models
2025-06-20 10:33:38,132 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2025-06-22 21:33:17,281 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2025-06-22 21:33:51,940 - server - INFO - get models
2025-06-22 21:33:53,999 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-22 21:33:54,001 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-22 21:33:54,003 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-22 21:33:57,946 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-22 21:34:06,653 - server - INFO - 报告C:\project\main\2025-06-22_21-34-06.md生成完成
2025-06-23 15:12:13,895 - server - INFO - get models
2025-06-23 15:12:16,814 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-23 15:12:16,815 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-23 15:12:16,819 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-23 15:12:44,533 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-23 15:12:44,535 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-23 15:12:44,538 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-23 15:13:48,696 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '保定相关政策', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-23 15:14:19,822 - server - INFO - request: /report, params: {'compare': 0, 'history': ['保定相关政策', '保定市作为河北省的重要城市之一，近年来在多个领域出台了一系列政策，旨在推动经济发展、改善民生、优化环境和提升城市治理水平。以下是一些可能的政策领域：\n\n1. **经济发展政策**：保定市可能会出台支持中小企业发展的政策，吸引外资和技术投资，推动产业升级和经济转型。\n\n2. **城市建设和规划**：为提高城市的宜居性，保定可能会在基础设施建设、公共交通改善和城市绿化等方面出台相关政策。\n\n3. **环境保护政策**：为了改善空气质量和生态环境，保定市可能会加强对工业排放的监管，推进可再生能源的使用，并实施一系列的环保项目。\n\n4. **教育和文化政策**：保定市可能会增加对教育资源的投入，提升教育质量，同时支持文化产业的发展，丰富市民的文化生活。\n\n5. **社会保障和民生政策**：为提高市民的生活质量，保定可能会在医疗、养老、住房保障等方面出台一系列政策措施。\n\n6. **科技创新政策**：保定市可能会鼓励科技创新，支持高新技术企业的发展，推动产学研结合，提升城市的科技竞争力。\n\n具体的政策内容会根据当地政府的发布和实施情况有所不同，建议关注保定市政府官方网站或相关政府公告，以获取最新和详细的政策信息。'], 'sessionId': 'admin_run_43'}
2025-06-23 15:14:28,370 - server - INFO - 报告C:\project\main\2025-06-23_15-14-28.md生成完成
2025-06-23 15:19:35,065 - server - INFO - request: /report, params: {'compare': 0, 'history': ['保定相关政策', '保定市作为河北省的重要城市之一，近年来在多个领域出台了一系列政策，旨在推动经济发展、改善民生、优化环境和提升城市治理水平。以下是一些可能的政策领域：\n\n1. **经济发展政策**：保定市可能会出台支持中小企业发展的政策，吸引外资和技术投资，推动产业升级和经济转型。\n\n2. **城市建设和规划**：为提高城市的宜居性，保定可能会在基础设施建设、公共交通改善和城市绿化等方面出台相关政策。\n\n3. **环境保护政策**：为了改善空气质量和生态环境，保定市可能会加强对工业排放的监管，推进可再生能源的使用，并实施一系列的环保项目。\n\n4. **教育和文化政策**：保定市可能会增加对教育资源的投入，提升教育质量，同时支持文化产业的发展，丰富市民的文化生活。\n\n5. **社会保障和民生政策**：为提高市民的生活质量，保定可能会在医疗、养老、住房保障等方面出台一系列政策措施。\n\n6. **科技创新政策**：保定市可能会鼓励科技创新，支持高新技术企业的发展，推动产学研结合，提升城市的科技竞争力。\n\n具体的政策内容会根据当地政府的发布和实施情况有所不同，建议关注保定市政府官方网站或相关政府公告，以获取最新和详细的政策信息。'], 'sessionId': 'admin_run_43'}
2025-06-23 15:19:44,233 - server - INFO - 报告C:\project\main\2025-06-23_15-19-44.md生成完成
2025-06-23 15:21:26,249 - server - INFO - request: /report, params: {'compare': '0', 'sessionId': 'admin_run_43', 'history': ['保定相关政策', '保定市作为河北省的重要城市之一，近年来在多个领域出台了一系列政策，旨在推动经济发展、改善民生、优化环境和提升城市治理水平。以下是一些可能的政策领域：\n\n1. **经济发展政策**：保定市可能会出台支持中小企业发展的政策，吸引外资和技术投资，推动产业升级和经济转型。\n\n2. **城市建设和规划**：为提高城市的宜居性，保定可能会在基础设施建设、公共交通改善和城市绿化等方面出台相关政策。\n\n3. **环境保护政策**：为了改善空气质量和生态环境，保定市可能会加强对工业排放的监管，推进可再生能源的使用，并实施一系列的环保项目。\n\n4. **教育和文化政策**：保定市可能会增加对教育资源的投入，提升教育质量，同时支持文化产业的发展，丰富市民的文化生活。\n\n5. **社会保障和民生政策**：为提高市民的生活质量，保定可能会在医疗、养老、住房保障等方面出台一系列政策措施。\n\n6. **科技创新政策**：保定市可能会鼓励科技创新，支持高新技术企业的发展，推动产学研结合，提升城市的科技竞争力。\n\n具体的政策内容会根据当地政府的发布和实施情况有所不同，建议关注保定市政府官方网站或相关政府公告，以获取最新和详细的政策信息。']}
2025-06-23 15:21:33,694 - server - INFO - 报告C:\project\main\2025-06-23_15-21-33.md生成完成
2025-06-23 15:25:02,542 - server - INFO - request: /report, params: {'compare': '0', 'sessionId': 'admin_run_43', 'history': ['保定相关政策', '保定市作为河北省的重要城市之一，近年来在多个领域出台了一系列政策，旨在推动经济发展、改善民生、优化环境和提升城市治理水平。以下是一些可能的政策领域：\n\n1. **经济发展政策**：保定市可能会出台支持中小企业发展的政策，吸引外资和技术投资，推动产业升级和经济转型。\n\n2. **城市建设和规划**：为提高城市的宜居性，保定可能会在基础设施建设、公共交通改善和城市绿化等方面出台相关政策。\n\n3. **环境保护政策**：为了改善空气质量和生态环境，保定市可能会加强对工业排放的监管，推进可再生能源的使用，并实施一系列的环保项目。\n\n4. **教育和文化政策**：保定市可能会增加对教育资源的投入，提升教育质量，同时支持文化产业的发展，丰富市民的文化生活。\n\n5. **社会保障和民生政策**：为提高市民的生活质量，保定可能会在医疗、养老、住房保障等方面出台一系列政策措施。\n\n6. **科技创新政策**：保定市可能会鼓励科技创新，支持高新技术企业的发展，推动产学研结合，提升城市的科技竞争力。\n\n具体的政策内容会根据当地政府的发布和实施情况有所不同，建议关注保定市政府官方网站或相关政府公告，以获取最新和详细的政策信息。']}
2025-06-23 15:25:10,631 - server - INFO - 报告C:\project\main\2025-06-23_15-25-10.md生成完成
2025-06-24 09:36:24,100 - server - INFO - get models
2025-06-24 09:36:57,332 - server - INFO - get models
2025-06-24 11:24:21,592 - server - INFO - get models
2025-06-24 11:26:21,155 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2025-06-24 15:02:59,484 - server - INFO - get models
2025-06-24 15:04:24,894 - server - INFO - get models
2025-06-24 15:07:29,091 - server - INFO - get models
2025-06-24 15:15:35,984 - server - INFO - get models
2025-06-24 15:15:39,165 - server - INFO - get models
2025-06-24 15:15:50,295 - server - INFO - get models
2025-06-24 15:17:19,160 - server - INFO - get models
2025-06-24 15:17:22,911 - server - INFO - get models
2025-06-24 15:17:23,131 - server - INFO - get models
2025-06-24 17:36:49,895 - server - INFO - get models
2025-06-25 08:54:52,410 - server - INFO - get models
2025-06-25 08:55:12,277 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-25 08:55:12,282 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-25 08:55:12,282 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-25 08:55:53,890 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-25 08:56:03,919 - server - INFO - 报告C:\project\main\2025-06-25_08-56-03.md生成完成
2025-06-25 08:58:29,286 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '人口情况如何', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-25 08:59:08,435 - server - INFO - get models
2025-06-25 08:59:11,111 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-25 08:59:11,112 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-25 08:59:11,113 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-25 08:59:43,427 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '人口状况如何', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-25 08:59:56,292 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['人口状况如何', '根据已知数据，保定市的人口状况如下：\n\n1. **历史常住人口**：\n   - 从2010年的1120.8113万人逐渐减少到2023年的909.89万人。\n   - 近年来，常住人口数量趋于稳定，预计未来几年将保持在909.89万人。\n\n2. **历史出生率**：\n   - 出生率在2010年为1.367%，经历了波动后，2020年及之后保持在0.694%。\n\n3. **历史死亡率**：\n   - 死亡率在2010年为0.733%，经历了波动后，2020年及之后保持在0.748%。\n\n4. **死亡人口**：\n   - 死亡人口在2010年为7.70万人，经历了一些波动后，预计到2050年将达到16.08万人。\n\n总体来看，保定市的人口数量在过去几年有所下降，出生率和死亡率相对稳定。未来几年常住人口预计会保持稳定。'], 'inputData': '出生率如何', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-25 09:00:34,735 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-25 09:00:34,738 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-25 09:00:34,738 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-25 09:00:37,387 - server - INFO - request: /report, params: {'compare': 0, 'history': ['人口状况如何', '根据已知数据，保定市的人口状况如下：\n\n1. **历史常住人口**：\n   - 从2010年的1120.8113万人逐渐减少到2023年的909.89万人。\n   - 近年来，常住人口数量趋于稳定，预计未来几年将保持在909.89万人。\n\n2. **历史出生率**：\n   - 出生率在2010年为1.367%，经历了波动后，2020年及之后保持在0.694%。\n\n3. **历史死亡率**：\n   - 死亡率在2010年为0.733%，经历了波动后，2020年及之后保持在0.748%。\n\n4. **死亡人口**：\n   - 死亡人口在2010年为7.70万人，经历了一些波动后，预计到2050年将达到16.08万人。\n\n总体来看，保定市的人口数量在过去几年有所下降，出生率和死亡率相对稳定。未来几年常住人口预计会保持稳定。', '出生率如何', '根据已知数据，保定市的出生率情况如下：\n\n- 2010年出生率为1.367%。\n- 在2011年，出生率略微上升至1.398%。\n- 从2012年开始，出生率经历了波动，2012年下降至1.327%，2013年回升至1.353%。\n- 2014年出生率进一步上升至1.389%。\n- 2015年出生率显著下降至1.091%。\n- 2016年出生率回升至1.222%。\n- 2017年出生率继续上升至1.335%。\n- 2018年出生率再次下降至1.063%。\n- 从2019年开始，出生率持续下降，2019年为1.023%，2020年下降至0.694%。\n- 从2020年起，出生率保持在0.694%，预计在未来几年继续保持这一水平。\n\n总体来看，保定市的出生率在过去十年中经历了一些波动，但从2020年起趋于稳定。'], 'sessionId': 'admin_run_43'}
2025-06-25 09:00:45,571 - server - INFO - 报告C:\project\main\2025-06-25_09-00-45.md生成完成
2025-06-25 09:40:38,372 - server - INFO - get models
2025-06-25 09:40:41,194 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-25 09:40:41,195 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-25 09:40:41,195 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-25 09:40:44,607 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-25 09:40:44,608 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-25 09:40:44,608 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-25 09:40:57,923 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '开始对话', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-25 09:41:04,540 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['开始对话', '您好！有什么我可以帮助您的吗？'], 'inputData': '政策', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-25 09:41:09,082 - server - INFO - request: /report, params: {'compare': 0, 'history': ['开始对话', '您好！有什么我可以帮助您的吗？', '政策', '关于政策，您是想了解某个特定领域的政策，比如经济、教育、环境等，还是需要某个地区的政策信息？请提供更多细节，以便我更好地帮助您。'], 'sessionId': 'admin_run_43'}
2025-06-25 09:41:15,634 - server - INFO - 报告C:\project\main\2025-06-25_09-41-15.md生成完成
2025-06-25 10:26:45,649 - server - INFO - get models
2025-06-25 10:26:47,718 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-25 10:26:47,719 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-25 10:26:47,720 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-25 10:26:54,717 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-25 10:26:54,719 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-25 10:26:54,720 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-25 10:27:20,896 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '你好', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-25 10:27:26,408 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['你好', '你好！有什么我可以帮助你的呢？'], 'inputData': '保定相关政策', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-25 10:27:37,048 - server - INFO - request: /report, params: {'compare': 0, 'history': ['你好', '你好！有什么我可以帮助你的呢？', '保定相关政策', '截至我最后的更新日期，我没有实时访问互联网的能力，因此无法提供最新的具体政策信息。不过，我可以为你提供一些保定市常见的政策领域和方向，供你参考：\n\n1. **经济发展政策**：保定市可能会制定促进经济增长的政策，包括支持中小企业发展、吸引外资、以及推动高新技术产业的发展。\n\n2. **环境保护政策**：保定市可能会实施政策以改善空气质量、保护水资源、以及推进绿色能源的使用。\n\n3. **城市建设与规划**：涉及城市基础设施建设、交通规划、以及城市扩张和更新改造的政策。\n\n4. **社会民生政策**：包括教育、医疗、住房保障等方面的政策，以提高居民生活质量。\n\n5. **文化与旅游发展政策**：保定市可能会通过政策来促进文化产业和旅游业的发展，利用当地的历史和自然资源。\n\n为了获取最新和具体的政策信息，建议查看保定市政府官方网站或相关政府部门发布的公告和新闻。'], 'sessionId': 'admin_run_43'}
2025-06-25 10:27:44,877 - server - INFO - 报告C:\project\main\2025-06-25_10-27-44.md生成完成
2025-06-25 10:29:35,489 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['你好', '你好！有什么我可以帮助你的呢？', '保定相关政策', '截至我最后的更新日期，我没有实时访问互联网的能力，因此无法提供最新的具体政策信息。不过，我可以为你提供一些保定市常见的政策领域和方向，供你参考：\n\n1. **经济发展政策**：保定市可能会制定促进经济增长的政策，包括支持中小企业发展、吸引外资、以及推动高新技术产业的发展。\n\n2. **环境保护政策**：保定市可能会实施政策以改善空气质量、保护水资源、以及推进绿色能源的使用。\n\n3. **城市建设与规划**：涉及城市基础设施建设、交通规划、以及城市扩张和更新改造的政策。\n\n4. **社会民生政策**：包括教育、医疗、住房保障等方面的政策，以提高居民生活质量。\n\n5. **文化与旅游发展政策**：保定市可能会通过政策来促进文化产业和旅游业的发展，利用当地的历史和自然资源。\n\n为了获取最新和具体的政策信息，建议查看保定市政府官方网站或相关政府部门发布的公告和新闻。'], 'inputData': '1', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-25 11:20:58,411 - server - INFO - get models
2025-06-25 11:21:02,547 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-25 11:21:02,550 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-25 11:21:02,554 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-25 11:21:15,153 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '人口状况', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-25 11:21:22,923 - server - INFO - request: /report, params: {'compare': 0, 'history': ['人口状况', '根据保定市的人口推演数据，以下是一些关键指标：\n\n1. **历史常住人口**：\n   - 2010年，保定市的常住人口为1120.8113万人。\n   - 从2010年到2023年，常住人口经历了波动，最高为1135.14万人（2012年），最低为909.89万人（2023年）。\n   - 从2024年到2050年，常住人口保持在909.89万人，没有变化。\n\n2. **历史出生率**：\n   - 出生率在2010年为1.367%。\n   - 出生率在2015年显著下降至1.091%，之后逐渐下降至2020年的0.694%，并在之后保持不变。\n\n3. **历史死亡率**：\n   - 死亡率在2010年为0.733%。\n   - 从2010年到2020年，死亡率经历了波动，最高为2014年的0.804%，最低为2015年的0.546%。\n   - 从2020年起，死亡率保持在0.748%不变。\n\n4. **历史平均预期寿命**：\n   - 从2010年到2050年，保定市的平均预期寿命保持在77.98岁，没有变化。\n\n总体来看，保定市的人口在未来的常住人口预计保持不变，而出生率和死亡率在近年来趋于稳定。平均预期寿命也保持稳定。'], 'sessionId': 'admin_run_43'}
2025-06-25 11:21:30,011 - server - INFO - 报告C:\project\main\2025-06-25_11-21-30.md生成完成
2025-06-25 11:22:11,318 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-25 11:22:11,319 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-25 11:22:11,319 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-25 11:22:38,485 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-25 11:22:46,682 - server - INFO - 报告C:\project\main\2025-06-25_11-22-46.md生成完成
2025-06-25 13:30:46,911 - server - ERROR - 发生异常: 系统内部异常，404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again., Path: /favicon.ico
2025-06-25 14:07:01,179 - server - INFO - get models
2025-06-25 14:07:34,222 - server - INFO - get models
2025-06-25 14:07:38,747 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-25 14:07:38,750 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-25 14:07:38,751 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-25 14:07:44,590 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '人口如何', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-25 14:07:55,217 - server - INFO - request: /report, params: {'compare': 0, 'history': ['人口如何', '根据保定市的推演数据，以下是一些关于人口的观察：\n\n1. **历史常住人口**：从2010年到2022年，保定市的常住人口经历了一些波动。2010年的人口为1120.8113万人，2013年出现显著下降，达到1022.93万人。此后，人口略有回升，但从2018年开始再次下降，直到2022年为914.4万人。从2023年到2050年，常住人口保持稳定在909.89万人。\n\n2. **出生率**：出生率从2010年的1.367%逐渐下降，到2019年为1.023%。此后，从2020年开始，出生率保持在0.694%，没有变化。\n\n3. **死亡率**：死亡率在2010年为0.733%，经历了一些波动，到2020年达到0.748%，此后保持不变。\n\n总体来看，保定市的人口在过去十年中有所下降，出生率和死亡率在最近几年保持稳定。未来的预测显示常住人口将保持稳定，但出生率和死亡率的变化可能会影响人口的自然增长。'], 'sessionId': 'admin_run_43'}
2025-06-25 14:08:02,645 - server - INFO - 报告C:\project\main\2025-06-25_14-08-02.md生成完成
2025-06-25 14:35:26,965 - server - INFO - get models
2025-06-25 14:35:31,889 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-25 14:35:31,890 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-25 14:35:31,891 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-25 14:35:39,200 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '人口如何', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-25 14:35:48,367 - server - INFO - request: /report, params: {'compare': 0, 'history': ['人口如何', '根据提供的数据，保定市的历史常住人口在2010年至2050年间有以下变化：\n\n- 2010年：1120.8113万人\n- 2011年：1127.23万人（增加6.42万人，增长率为0.57%）\n- 2012年：1135.14万人（增加7.91万人，增长率为0.70%）\n- 2013年：1022.93万人（减少112.21万人，下降率为9.89%）\n- 2014年：1029.5万人（增加6.57万人，增长率为0.64%）\n- 2015年：1034.9万人（增加5.40万人，增长率为0.52%）\n- 2016年：1042.5万人（增加7.60万人，增长率为0.73%）\n- 2017年：1046.92万人（增加4.42万人，增长率为0.42%）\n- 2018年：935.93万人（减少110.99万人，下降率为10.60%）\n- 2019年：939.91万人（增加3.98万人，增长率为0.43%）\n- 2020年：924.261万人（减少15.65万人，下降率为1.66%）\n- 2021年：919.5万人（减少4.76万人，下降率为0.52%）\n- 2022年：914.4万人（减少5.10万人，下降率为0.55%）\n- 2023年：909.89万人（减少4.51万人，下降率为0.49%）\n\n从2024年到2050年，常住人口保持稳定在909.89万人，没有变化。\n\n总体来看，保定市的人口在这段时间内经历了一些波动，特别是在2013年和2018年出现了显著的下降。此后，人口趋于稳定，并在2024年之后保持不变。'], 'sessionId': 'admin_run_43'}
2025-06-25 14:35:57,520 - server - INFO - 报告C:\project\main\2025-06-25_14-35-57.md生成完成
2025-06-26 09:57:12,595 - server - INFO - get models
2025-06-26 10:00:54,984 - server - INFO - get models
2025-06-26 10:57:51,849 - server - INFO - get models
2025-06-26 16:13:09,381 - server - INFO - get models
2025-06-26 16:13:12,100 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-26 16:13:12,103 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-26 16:13:12,103 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-26 16:13:17,379 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-26 16:13:17,380 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-26 16:13:17,380 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-26 16:13:32,631 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '你好', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-26 16:13:40,767 - server - INFO - request: /chat, params: {'dataType': 1, 'history': ['你好', '你好！有什么我可以帮助你的呢？'], 'inputData': '保定政策', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-26 16:13:47,725 - server - INFO - request: /report, params: {'compare': 0, 'history': ['你好', '你好！有什么我可以帮助你的呢？', '保定政策', '保定市的政策涉及多个方面，包括经济发展、环境保护、社会福利、教育和城市规划等。以下是一些可能的政策方向：\n\n1. **经济发展**：保定市可能会实施促进经济增长的政策，如支持中小企业发展、吸引外资、推动科技创新等。\n\n2. **环境保护**：为改善空气质量和生态环境，保定市可能会加强环保措施，如减少污染排放、推广绿色能源、保护自然资源等。\n\n3. **社会福利**：保定市可能会加强社会保障体系，改善医疗和养老服务，提高居民生活质量。\n\n4. **教育**：保定市可能会投资教育设施，提高教育质量，推动职业教育和培训，以满足市场需求。\n\n5. **城市规划**：保定市可能会进行城市规划和基础设施建设，以改善交通、住房和公共服务。\n\n具体政策可能会根据政府的规划和社会需求而变化。有关保定市最新政策的信息，建议查阅当地政府官方网站或相关新闻报道。'], 'sessionId': 'admin_run_43'}
2025-06-26 16:13:56,612 - server - INFO - 报告C:\project\main\2025-06-26_16-13-56.md生成完成
2025-06-26 16:15:38,569 - server - INFO - get models
2025-06-26 16:15:42,274 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-26 16:15:42,275 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-26 16:15:42,276 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-26 21:52:24,561 - server - INFO - get models
2025-06-26 21:52:28,358 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-26 21:52:28,360 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-26 21:52:28,361 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-26 21:52:31,303 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-26 21:52:41,989 - server - INFO - 报告C:\project\main\2025-06-26_21-52-41.md生成完成
2025-06-27 14:07:43,958 - server - INFO - get models
2025-06-30 09:11:36,031 - server - INFO - get models
2025-06-30 09:11:46,767 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-30 09:11:46,770 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-30 09:11:46,770 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-30 09:12:22,115 - server - INFO - request: /report, params: {'compare': 1, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-30 09:12:23,008 - server - ERROR - 发生异常: 系统内部异常，Error code: 403 - {'error': {'message': '账户余额过低不足以支持本次请求，请前往 https://api.chatanywhere.tech/#/shop 充值。Your account balance is not sufficient to support this request. Please visit https://api.chatanywhere.tech/#/shop to recharge.(当前请求使用的ApiKey: sk-lHg****05F0)【如果您遇到问题，欢迎加入QQ群咨询：**********】', 'type': 'chatanywhere_error', 'param': None, 'code': '403 FORBIDDEN'}}, Path: /report
2025-06-30 09:12:28,686 - server - INFO - request: /report, params: {'compare': 1, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-30 09:12:29,351 - server - ERROR - 发生异常: 系统内部异常，Error code: 403 - {'error': {'message': '账户余额过低不足以支持本次请求，请前往 https://api.chatanywhere.tech/#/shop 充值。Your account balance is not sufficient to support this request. Please visit https://api.chatanywhere.tech/#/shop to recharge.(当前请求使用的ApiKey: sk-lHg****05F0)【如果您遇到问题，欢迎加入QQ群咨询：**********】', 'type': 'chatanywhere_error', 'param': None, 'code': '403 FORBIDDEN'}}, Path: /report
2025-06-30 09:12:33,150 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-30 09:12:33,759 - server - ERROR - 发生异常: 系统内部异常，Error code: 403 - {'error': {'message': '账户余额过低不足以支持本次请求，请前往 https://api.chatanywhere.tech/#/shop 充值。Your account balance is not sufficient to support this request. Please visit https://api.chatanywhere.tech/#/shop to recharge.(当前请求使用的ApiKey: sk-lHg****05F0)【如果您遇到问题，欢迎加入QQ群咨询：**********】', 'type': 'chatanywhere_error', 'param': None, 'code': '403 FORBIDDEN'}}, Path: /report
2025-06-30 09:12:40,110 - server - INFO - request: /chat, params: {'dataType': 1, 'history': [], 'inputData': '123', 'outputType': 1, 'sessionId': 'admin_run_43'}
2025-06-30 09:12:40,690 - server - ERROR - 发生异常: 系统内部异常，Error code: 403 - {'error': {'message': '账户余额过低不足以支持本次请求，请前往 https://api.chatanywhere.tech/#/shop 充值。Your account balance is not sufficient to support this request. Please visit https://api.chatanywhere.tech/#/shop to recharge.(当前请求使用的ApiKey: sk-lHg****05F0)【如果您遇到问题，欢迎加入QQ群咨询：**********】', 'type': 'chatanywhere_error', 'param': None, 'code': '403 FORBIDDEN'}}, Path: /chat
2025-06-30 11:14:07,369 - server - INFO - get models
2025-06-30 11:17:17,086 - server - INFO - get models
2025-06-30 14:43:40,515 - server - INFO - get models
2025-06-30 14:58:00,267 - server - INFO - get models
2025-06-30 14:58:07,790 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-06-30 14:58:07,792 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-06-30 14:58:07,792 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-06-30 14:58:21,075 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-06-30 14:58:22,006 - server - ERROR - 发生异常: 系统内部异常，Error code: 403 - {'error': {'message': '账户余额过低不足以支持本次请求，请前往 https://api.chatanywhere.tech/#/shop 充值。Your account balance is not sufficient to support this request. Please visit https://api.chatanywhere.tech/#/shop to recharge.(当前请求使用的ApiKey: sk-lHg****05F0)【如果您遇到问题，欢迎加入QQ群咨询：**********】', 'type': 'chatanywhere_error', 'param': None, 'code': '403 FORBIDDEN'}}, Path: /report
2025-07-02 12:44:22,941 - server - INFO - get models
2025-07-02 12:44:25,873 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'gpt-4o-ca', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-07-02 12:44:25,874 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-07-02 12:44:25,875 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-07-02 12:44:33,333 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-07-02 12:44:43,052 - server - INFO - 报告C:\project\main\2025-07-02_12-44-43.md生成完成
2025-07-02 12:45:13,503 - server - INFO - request: /initModel, params: {'datafile': 'C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_value.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\symbol_relationship.csv;C:\\fangzhen\\admin\\19_qj3_test08051044\\PYSD_project\\data\\resultdata\\exp保定市\\csv\\result_data\\PolicyDeduction\\保定市1\\symbol_value.csv', 'indicators': '死亡人口, GDP, 历史常住人口, 历史出生率, 历史死亡率, 历史平均预期寿命', 'knowledgeBase': '', 'knowledgeGraph': 0, 'llm': 'deepseek-r1:7b', 'maxTokens': 10000, 'parameters': {'第一产业GDP': 1, '历史发电量': 1, '历史城镇人口': 1, '历史农村人口': 1, '基础死亡率': 1}, 'region': '保定市', 'searchEngine': 0, 'sessionId': 'admin_run_43', 'temperature': 0.6, 'template': '', 'topP': 0.8}
2025-07-02 12:45:13,504 - server - INFO - 知识库已禁用，搜索引擎已禁用，知识图谱已禁用
2025-07-02 12:45:13,504 - server - INFO - indicators: ['死亡人口', 'GDP', '历史常住人口', '历史出生率', '历史死亡率', '历史平均预期寿命']
2025-07-02 12:45:16,629 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-07-02 12:45:34,703 - server - ERROR - 发生异常: 系统内部异常，Request timed out., Path: /report
2025-07-02 12:45:40,795 - server - INFO - request: /report, params: {'compare': 0, 'history': [], 'sessionId': 'admin_run_43'}
2025-07-02 12:45:58,869 - server - ERROR - 发生异常: 系统内部异常，Request timed out., Path: /report
2025-07-02 12:48:42,229 - server - INFO - get models
2025-07-02 18:03:13,163 - server - INFO - get models
