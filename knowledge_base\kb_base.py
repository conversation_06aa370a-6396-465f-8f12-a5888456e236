# -*- coding: utf-8 -*-
"""
知识库服务基类
"""
import os
from abc import ABC, abstractmethod
from pathlib import Path
from langchain.docstore.document import Document
from utils.embedding_utils import embed_documents
from utils.knowledge_base_utils import KnowledgeFile, get_kb_path, get_doc_path
from knowledge_base.kb_doc import DocumentWithVSId
from typing import List, Dict
from configs.local_model_config import VECTOR_SEARCH_TOP_K, SCORE_THRESHOLD, EMBEDDING_MODEL

from utils.db_utils import remove_all_docs

class KBService(ABC):

    def __init__(self,
                 knowledge_base_name: str,
                 embed_model: str = EMBEDDING_MODEL,
                 ):
        self.kb_name = knowledge_base_name
        self.kb_info = "测试知识库"
        self.embed_model = embed_model
        self.kb_path = get_kb_path(self.kb_name)
        self.doc_path = get_doc_path(self.kb_name)
        self.do_init()

    def __repr__(self) -> str:
        return f"{self.kb_name} @ {self.embed_model}"

    def save_vector_store(self):
        '''
        保存向量库:FAISS保存到磁盘，milvus保存到数据库。PGVector暂未支持
        '''
        pass

    def create_kb(self):
        """
        创建知识库
        """
        if not os.path.exists(self.doc_path):
            os.makedirs(self.doc_path)
        self.do_create_kb()
        # status = add_kb_to_db(self.kb_name, self.kb_info, self.vs_type(), self.embed_model)
        # return status
        return True

    def clear_vs(self):
        """
        删除向量库中所有内容
        """
        self.do_clear_vs()
        # status = delete_files_from_db(self.kb_name)
        # return status

        remove_all_docs(self.kb_name)

        return True

    def drop_kb(self):
        """
        删除知识库
        """
        self.do_drop_kb()
        # status = delete_kb_from_db(self.kb_name)
        # return status
        return None

    def _docs_to_embeddings(self, docs: List[Document]) -> Dict:
        '''
        将 List[Document] 转化为 VectorStore.add_embeddings 可以接受的参数
        '''
        return embed_documents(docs=docs, embed_model=self.embed_model, to_query=False)

    def add_doc(self, kb_file: KnowledgeFile, docs: List[Document] = [], **kwargs):
        """
        向知识库添加文件
        如果指定了docs，则不再将文本向量化，并将数据库对应条目标为custom_docs=True
        """
        if docs:
            custom_docs = True
            for doc in docs:
                doc.metadata.setdefault("source", kb_file.filename)
        else:
            docs = kb_file.file2text()
            custom_docs = False

        if docs:
            # 将 metadata["source"] 改为相对路径
            for doc in docs:
                try:
                    source = doc.metadata.get("source", "")
                    rel_path = Path(source).relative_to(self.doc_path)
                    doc.metadata["source"] = str(rel_path.as_posix().strip("/"))
                except Exception as e:
                    print(f"cannot convert absolute path ({source}) to relative path. error is : {e}")
            self.delete_doc(kb_file)
            doc_infos = self.do_add_doc(docs, **kwargs)
            # status = add_file_to_db(kb_file,
            #                         custom_docs=custom_docs,
            #                         docs_count=len(docs),
            #                         doc_infos=doc_infos)
        else:
            # status = False
            pass
        # return status
        return True

    def delete_doc(self, kb_file: KnowledgeFile, delete_content: bool = False, **kwargs):
        """
        从知识库删除文件
        """
        self.do_delete_doc(kb_file, **kwargs)
        # status = delete_file_from_db(kb_file)
        if delete_content and os.path.exists(kb_file.filepath):
            os.remove(kb_file.filepath)
        # return status
        return True

    def update_info(self, kb_info: str):
        """
        更新知识库介绍
        """
        self.kb_info = kb_info
        # status = add_kb_to_db(self.kb_name, self.kb_info, self.vs_type(), self.embed_model)
        # return status
        return True

    def update_doc(self, kb_file: KnowledgeFile, docs: List[Document] = [], **kwargs):
        """
        使用content中的文件更新向量库
        如果指定了docs，则使用自定义docs，并将数据库对应条目标为custom_docs=True
        """
        if os.path.exists(kb_file.filepath):
            self.delete_doc(kb_file, **kwargs)
            return self.add_doc(kb_file, docs=docs, **kwargs)

    def exist_doc(self, file_name: str):
        # return file_exists_in_db(KnowledgeFile(knowledge_base_name=self.kb_name,filename=file_name))
        return False

    def list_files(self):
        # return list_files_from_db(self.kb_name)
        return []

    def count_files(self):
        # return count_files_from_db(self.kb_name)
        return 0

    def search_docs(self,
                    query: str,
                    top_k: int = VECTOR_SEARCH_TOP_K,
                    score_threshold: float = SCORE_THRESHOLD,
                    ):
        docs = self.do_search(query, top_k, score_threshold)
        return docs

    def get_doc_by_ids(self, ids: List[str]) -> List[Document]:
        return []

    def list_docs(self, file_name: str = None, metadata: Dict = {}) -> List[DocumentWithVSId]:
        '''
        通过file_name或metadata检索Document
        '''
        # doc_infos = list_docs_from_db(kb_name=self.kb_name, file_name=file_name, metadata=metadata)
        docs = []
        # for x in doc_infos:
        #     doc_info_s = self.get_doc_by_ids([x["id"]])
        #     if doc_info_s is not None and doc_info_s != []:
        #         # 处理非空的情况
        #         doc_with_id = DocumentWithVSId(**doc_info_s[0].dict(), id=x["id"])
        #         docs.append(doc_with_id)
        #     else:
        #         # 处理空的情况
        #         # 可以选择跳过当前循环迭代或执行其他操作
        #         pass
        return docs

    @abstractmethod
    def do_create_kb(self):
        """
        创建知识库子类实自己逻辑
        """
        pass

    @staticmethod
    def list_kbs_type():
        # return list(kbs_config.keys())
        return []

    @classmethod
    def list_kbs(cls):
        # return list_kbs_from_db()
        return []

    def exists(self, kb_name: str = None):
        kb_name = kb_name or self.kb_name
        # return kb_exists(kb_name)
        return True

    @abstractmethod
    def vs_type(self) -> str:
        pass

    @abstractmethod
    def do_init(self):
        pass

    @abstractmethod
    def do_drop_kb(self):
        """
        删除知识库子类实自己逻辑
        """
        pass

    @abstractmethod
    def do_search(self,
                  query: str,
                  top_k: int,
                  score_threshold: float,
                  ) -> List[Document]:
        """
        搜索知识库子类实自己逻辑
        """
        pass

    @abstractmethod
    def do_add_doc(self,
                   docs: List[Document],
                   ) -> List[Dict]:
        """
        向知识库添加文档子类实自己逻辑
        """
        pass

    @abstractmethod
    def do_delete_doc(self,
                      kb_file: KnowledgeFile):
        """
        从知识库删除文档子类实自己逻辑
        """
        pass

    @abstractmethod
    def do_clear_vs(self):
        """
        从知识库删除全部向量子类实自己逻辑
        """
        pass