<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>知识库文档管理</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap.min.css') }}">
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
</head>
<body>
<div class="container mt-5">
    <h1 class="mb-4">知识库文档管理</h1>

    <form action="{{ url_for('doc_blueprint.upload_files') }}" method="post" enctype="multipart/form-data">
        <div class="row mb-3">
            <div class="col-md-3 mb-2">
                <input type="file" name="files" class="form-control-file" id="file" multiple>
            </div>
            <div class="col-md-3 mb-2">
                <button type="submit" class="btn btn-primary btn-block">上传文档</button>
            </div>
        </div>
    </form>
    <hr>
    <div class="mb-4">
        <a id="initialize_kb_btn" href="{{ url_for('doc_blueprint.initialize_kb') }}" class="btn btn-primary">初始化知识库</a>
        <a id="update_kb_btn" href="{{ url_for('doc_blueprint.update_kb') }}" class="btn btn-primary">更新知识库</a>
    </div>
    <h2>知识库文档</h2>
    <table class="table table-bordered">
        <thead>
        <tr>
            <th>文件名</th>
            <th>向量化时间</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody id="doc_list">
        {% for file in file_info %}
        <tr>
            <td>
                <a href="{{ url_for('doc_blueprint.uploaded_file', filename=file.filename) }}">{{ file.filename }}</a>
            </td>
            <td>{{ file.mod_time }}</td>
            <td>
                <button type="button" class="btn btn-danger btn-sm" data-file="{{ file.filename }}">
                    删除
                </button>
            </td>
        </tr>
        {% endfor %}
        </tbody>
    </table>

    {% with messages = get_flashed_messages() %}
    {% if messages %}
    <div class="alert alert-info alert-dismissible fade show mt-3" role="alert">
        {% for message in messages %}
        <p>{{ message }}</p>
        {% endfor %}
        <button type="button" class="btn-close" data-dismiss="alert" aria-label="Close"></button>
    </div>
    {% endif %}
    {% endwith %}
</div>

<!-- 删除确认 -->
<div class="modal fade" id="delete_modal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个文件吗？
            </div>
            <div class="modal-footer">
                <form id="delete_form" method="post">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 遮罩 -->
<div class="modal fade" id="loading_modal" tabindex="-1" data-backdrop="static" aria-labelledby="loadingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content text-center">
            <div class="modal-body">
                <h5 class="mb-3" style="cursor: pointer;">加载中...</h5>
                <div class="spinner-border" role="status">
                    <span class="sr-only"></span>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='jquery-3.3.1.min.js') }}"></script>
<script src="{{ url_for('static', filename='popper.min.js') }}"></script>
<script src="{{ url_for('static', filename='bootstrap.min.js') }}"></script>
<script>
    $(document).ready(function() {
        function showLoading() {
            $("#loading_modal").modal("show");
        }

        function hideLoading() {
            $("#loading_modal").modal("hide");
        }

        $("#doc_list button").on("click", function(e){
            $("#delete_modal").modal("show");
            console.log(e);
            var btn = $(e.delegateTarget);
            console.log(btn);
            var file = btn.data("file");
            console.log(file);
            var actionUrl = "{{ url_for('doc_blueprint.delete_file', filename='') }}" + file;
            $("#delete_form").attr("action", actionUrl);
        });

        $("#initialize_kb_btn, #update_kb_btn").on("click", function(e) {
            showLoading();
        });
    });
</script>
</body>
</html>
