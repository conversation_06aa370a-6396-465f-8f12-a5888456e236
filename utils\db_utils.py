# -*- coding: utf-8 -*-
"""
知识库维护信息持久化工具类
"""
import os
import json
import datetime
from utils.knowledge_base_utils import get_kb_path
from configs.app_config import DEFAULT_KB_NAME

DB_FILENAME = "db.json"


def get_doc_info(kb_name=DEFAULT_KB_NAME):
    """
    获取数据库中的文档信息
    """
    kb_path = get_kb_path(kb_name)
    db_path = os.path.join(kb_path, DB_FILENAME)

    # 初始化db文件
    if not os.path.exists(db_path):
        os.makedirs(kb_path)
        data = {}
        with open(db_path, 'w') as json_file:
            json.dump(data, json_file)
        return data

    with open(db_path, 'r') as file:
        data = json.load(file)
    return data


def update_doc_info(docs, kb_name=DEFAULT_KB_NAME):
    """
    更新数据库中的文档记录
    """
    kb_path = get_kb_path(kb_name)
    db_path = os.path.join(kb_path, DB_FILENAME)
    with open(db_path, 'r') as file:
        data = json.load(file)
    current_time = datetime.datetime.now()
    formatted_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
    for doc in docs:
        data[doc] = formatted_time
    with open(db_path, 'w') as file:
        json.dump(data, file)


def remove_doc_info(doc, kb_name=DEFAULT_KB_NAME):
    """
    删除数据库中某条文档记录
    """
    kb_path = get_kb_path(kb_name)
    db_path = os.path.join(kb_path, DB_FILENAME)
    with open(db_path, 'r') as file:
        data = json.load(file)
    removed_value = data.pop(doc, None)
    with open(db_path, 'w') as file:
        json.dump(data, file)


def remove_all_docs(kb_name=DEFAULT_KB_NAME):
    """
    清空数据库中所有文档记录
    :param kb_name:
    """
    kb_path = get_kb_path(kb_name)
    db_path = os.path.join(kb_path, DB_FILENAME)
    with open(db_path, 'w') as file:
        json.dump({}, file)
