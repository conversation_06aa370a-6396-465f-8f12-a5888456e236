from openai import OpenAI
import re

history = []
client = OpenAI(api_key='ollama',base_url='http://192.168.1.58:31435/v1/')
# client = OpenAI(api_key='sk-xtbikqyekmbmdrxtcdzyblynkgxofpacrljoibiknixyczvu', base_url='https://api.siliconflow.cn/v1')
messages = [{'role': 'system', 'content': '你是一个城市仿真推演相关的数据分析e专家，精通数据分析以及报告撰写'}]
for i in range(0, len(history), 2):
    pair = history[i:i + 2]
    messages.append({'role': 'user', 'content': history[i]})
    messages.append({'role': 'assistant', 'content': history[i+1]})
messages.append({'role': 'user', 'content': '你好'})
completion = client.chat.completions.create(model='deepseek-r1:70b', messages=messages,  #  deepseek-ai/DeepSeek-R1
                                            temperature=0.8, top_p=0.8,
                                            max_tokens=10000)
response = completion.choices[0].message.content
response = re.sub(r'<think>.*?</think>\s*', '', response, flags=re.DOTALL)
print(response)
