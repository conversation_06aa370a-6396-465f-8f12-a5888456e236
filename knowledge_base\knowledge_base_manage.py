# -*- coding: utf-8 -*-
import os
import shutil
# from text_splitter import zh_title_enhance as func_zh_title_enhance
from typing import Literal, List
from utils.knowledge_base_utils import KnowledgeFile, file_to_kbfile, files2docs_in_thread, get_doc_path
from knowledge_base.kb_service import FaissKBService
from configs.local_model_config import CHUNK_SIZE, OVERLAP_SIZE, ZH_TITLE_ENHANCE, EMBEDDING_MODEL
from configs.app_config import DEFAULT_KB_NAME
from utils.db_utils import get_doc_info, update_doc_info


def folder2db(
        mode: Literal["recreate_vs", "increment"],
        chunk_size: int = CHUNK_SIZE,  # 单段文本长度
        chunk_overlap: int = OVERLAP_SIZE,  # 相邻文本重合长度
        zh_title_enhance: bool = ZH_TITLE_ENHANCE,  # 是否开启中文标题加强，以及标题增强的相关配置，通过增加标题判断，判断哪些文本为标题，并在metadata中进行标记；然后将文本与往上一级的标题进行拼合，实现文本信息的增强。
):
    """
    根据知识库名获取知识库对应目录下的文档并进行向量持久化
    recreate_vs：根据全量文档重新构建向量数据库
    increment：根据文件上传目录中的文档更新向量数据库
    """

    def files2vs(kb_name: str, kb_files: List[KnowledgeFile]):
        for success, result in files2docs_in_thread(kb_files,
                                                    chunk_size=chunk_size,
                                                    chunk_overlap=chunk_overlap,
                                                    zh_title_enhance=zh_title_enhance):
            if success:
                _, filename, docs = result
                print(f"正在将 {kb_name}/{filename} 添加到向量库，共包含{len(docs)}条文档")
                kb_file = KnowledgeFile(filename=filename, knowledge_base_name=kb_name)
                kb_file.splited_docs = docs
                kb.add_doc(kb_file=kb_file, not_refresh_vs_cache=True)
            else:
                print(result)

    kb_name = DEFAULT_KB_NAME
    # 初始化向量存储
    kb = FaissKBService(kb_name, embed_model=EMBEDDING_MODEL)
    if not kb.exists():
        kb.create_kb()

    # 清除向量库，从本地文件重建
    if mode == "recreate_vs":
        kb.clear_vs()
        kb.create_kb()
        files = os.listdir(get_doc_path(kb_name))
        # files = list_files_from_folder(kb_name)
        kb_files = file_to_kbfile(kb_name, files)
        files2vs(kb_name, kb_files)
        kb.save_vector_store()
        # 保存文档信息到数据库
        update_doc_info(files)
    # 对比本地目录与数据库中的文件列表，进行增量向量化
    elif mode == "increment":

        db_docs = get_doc_info()
        folder_docs = os.listdir(get_doc_path(kb_name))

        # 对比本地文档与记录中文档的差异
        docs = [item for item in folder_docs if item not in db_docs]
        print(f"increment docs: {docs}")
        kb_files = file_to_kbfile(kb_name, docs)
        files2vs(kb_name, kb_files)  # 将文件添加到向量数据库
        kb.save_vector_store()
        update_doc_info(docs)
    else:
        print(f"unspported migrate mode: {mode}")


if __name__ == '__main__':
    folder2db(mode="recreate_vs")