# -*- coding: utf-8 -*-
from objects.exceptions import ParameterInvalidException

import os
import datetime
import pandas as pd
from openai import OpenAI

from configs.app_config import DEFAULT_KB_NAME, LLM_APIS, REPORT_DIR
from configs.local_model_config import EMBEDDING_MODEL

from knowledge_base.kb_service import FaissKBService
from knowledge_base.kb_doc import DocumentWithScore
from utils.common_utils import parse_data
from utils.embedding_utils import search_result2docs
from utils.se_utils import search_from_internet

from local_model import model, tokenizer

import re

class ChatManager:
    """
    管理所有聊天实例
    TODO: bot生命周期的维护
    """
    def __init__(self, logger):
        self.bots = {}
        self.logger = logger
        # 初始化报告生成目录
        if not os.path.exists(REPORT_DIR):
            os.makedirs(REPORT_DIR)
            self.logger.info(f"初始化报告生成目录：{REPORT_DIR}")

    def create_bot(self, bot_id, params):
        bot = ChatBot(bot_id, params, self.logger)
        self.bots[bot_id] = bot

    def get_bot(self, bot_id):
        return self.bots.get(bot_id)


class ChatBot:
    """
    报告聊天机器人
    参数说明：
            id: bot实例唯一标识
            region: 地区
            indicators: 指标项名称，多个指标用“,”（半角逗号）分隔，在输入类型为表格文件时，用于从表格中根据指标项名称提取数据
            datafiles: 默认推演及调参推演文件绝对路径，两个路径之间用“;”分隔，三个数据文件：默认推演数据（必需）、数据单位、调参推演数据
            template: 报告模板文件的绝对路径，如果为空，使用默认模板
            parameters: 调参推演使用的推演系数
            llm: 使用的基座模型，套壳API支持的模型或本地部署的模型的名称
            max_tokens: 模型参数，最大token数，用于控制调用API的开销
            temperature: 模型参数，采样温度，用于调整输出结果的概率分布，在[0.0,2.0]之间
            top_p: 模型参数，生成的token时使用的概率排序阈值，在[0.0,1.0]之间
            knowledge_base: 知识库对应的目录的路径，如果为空，不使用知识库
            search_engine: 是否启用搜索引擎，0：不启用；1：启用。默认不启用
            knowledge_graph: 是否使用知识图谱，0：不启用；1：启用。默认不启用
    """
    def __init__(self, bot_id, params, logger):

        self.logger = logger

        self.id = bot_id
        self.region = params.get("region")
        self.indicators = params.get("indicators")
        self.datafiles = params.get("datafile")
        self.template = params.get("template")
        self.parameters = params.get("parameters")
        self.llm = params.get("llm")
        self.max_tokens = params.get("maxTokens", 10000)
        self.temperature = params.get("temperature", 0.6)
        self.top_p = params.get("topP", 0.8)
        self.knowledge_base = params.get("knowledgeBase")
        self.search_engine = params.get("searchEngine", 0)
        self.knowledge_graph = params.get("knowledgeGraph", 0)

        # 校验数据
        # 必需项校验
        if not self.region:
            raise ParameterInvalidException("地区不能为空")
        if not self.indicators:
            raise ParameterInvalidException("指标项不能为空")
        if not self.datafiles:
            raise ParameterInvalidException("数据文件路径不能为空")
        if not self.parameters:
            raise ParameterInvalidException("推演参数不能为空")
        if not self.llm:
            raise ParameterInvalidException("模型不能为空")
        if self.temperature and (self.temperature < 0 or self.temperature > 2):
            raise ParameterInvalidException("temperature参数值无效")
        if self.top_p and (self.top_p < 0 or self.top_p > 1):
            raise ParameterInvalidException("top p参数值无效")

        # 可选项默认赋值
        # self.enable_knowledge_base = bool(self.knowledge_base)
        # 目前环境禁用kb
        self.enable_knowledge_base = False
        self.enable_search_engine = bool(self.search_engine)
        self.enable_knowledge_graph = bool(self.knowledge_graph)

        self.logger.info(f"知识库已{'启用' if self.enable_knowledge_base else '禁用'}，搜索引擎已{'启用' if self.enable_search_engine else '禁用'}，知识图谱已{'启用' if self.enable_knowledge_graph else '禁用'}")

        # 处理数据
        self.indicators = self.indicators.split(",")
        self.datafiles = self.datafiles.split(";")

        self.indicators = [str(item).strip() for item in self.indicators]
        self.logger.info(f"indicators: {self.indicators}")

        # if len(self.datafiles) != 3:
        #     raise ParameterInvalidException("应包含3个数据文件路径")

        for datafile in self.datafiles:
            _, extension = os.path.splitext(datafile)
            if extension != ".csv":
                raise ParameterInvalidException("数据文件应为csv格式")

        default_data_file_path = self.datafiles[0]
        unit_file_path = self.datafiles[1]
        tuned_data_file_path = self.datafiles[2] if len(self.datafiles) > 2 else None

        if not default_data_file_path or not os.path.exists(default_data_file_path):
            raise ParameterInvalidException("缺少默认推演文件")

        default_data_frame = pd.read_csv(default_data_file_path)
        default_columns = default_data_frame.columns
        default_years = default_data_frame[default_columns[0]]

        # 根据变量名取单位
        if unit_file_path and os.path.exists(unit_file_path): 
            df = pd.read_csv(unit_file_path)
            self.units = {}
            for indicator in self.indicators:
                row = df[df['变量名'] == indicator]

                if not row.empty:
                    unit_value = row['单位'].values[0]
                    self.units[indicator] = unit_value
                else:
                    self.units[indicator] = ""
            print(f"units: {self.units}")

        tuned_data_frame = None
        tuned_columns = None
        tuned_years = None
        # 读取调参数据
        if tuned_data_file_path and os.path.exists(tuned_data_file_path):
            tuned_data_frame = pd.read_csv(tuned_data_file_path)
            tuned_columns = tuned_data_frame.columns
            tuned_years = tuned_data_frame[tuned_columns[0]]

        self.default_data = {}
        self.tuned_data = {}

        for indicator in self.indicators:
            # if indicator not in default_columns or indicator not in tuned_columns:
            #     raise ParameterInvalidException(f"指标项{indicator}在数据文件中不存在")

            if indicator in default_columns:
                self.default_data[indicator] = {
                    "data": parse_data(default_years, default_data_frame[indicator]),
                    "unit": self.units[indicator]
                }
            if tuned_columns is not None and indicator in tuned_columns:
                self.tuned_data[indicator] = {
                    "data": parse_data(tuned_years, tuned_data_frame[indicator]),
                    "unit": self.units[indicator]
                }

        # print(f"default data: {self.default_data}")
        # print(f"tuned data: {self.tuned_data}")

        # 读取模板
        self.template_content = ""
        # test
        # self.template = "C:\\project\\main\\template.md"
        if self.template:
            # 检查模板文件格式
            _, extension = os.path.splitext(self.template)
            if extension not in [".txt", ".md", ".markdown", ".MD", ".MARKDOWN"]:
                raise ParameterInvalidException(f"模板文件仅支持txt、markdown格式")
            # 读取模板文件内容
            with open(self.template, 'r', encoding='utf-8') as file:
                self.template_content = file.read()
            print(f"template content: {self.template_content}")


    def chat(self, query, history):
        """
        聊天机器人接口，上下文由两部分组成，与用户交互的历史以及推演数据
        :param query: 用户输入的问题
        :param history: 历史问答记录
        :return: 生成文本
        """
        result = {}

        # 构造prompt
        prompt = f"已知{self.region}有以下指标推演数据：{self.default_data}"
        if self.tuned_data:
            prompt += f"，以及优化后的推演结果：{self.tuned_data}"

        # 知识库嵌入
        kb_content = ""
        if self.enable_knowledge_base:
            service = FaissKBService(DEFAULT_KB_NAME, embed_model=EMBEDDING_MODEL)
            top_k = 3 # 匹配向量数
            score_threshold = 1 # 匹配相关度阈值
            docs = service.search_docs(query, top_k, score_threshold) # 根据指标项在知识库中检索
            knowledge_data = [DocumentWithScore(**x[0].dict(), score=x[1]) for x in docs]
            kb_content = "\n".join([doc.page_content for doc in knowledge_data])

        # 搜索引擎嵌入
        # 每个指标项，查找相关的
        #TODO: 通过大模型构建检索关键词
        se_content = ""
        if self.enable_search_engine:
            results = search_from_internet(query)
            docs = search_result2docs(results)
            se_content = "\n".join([doc.page_content for doc in docs])

        # 重构prompt，加入外部知识
        if kb_content or se_content:
            prompt = f"{prompt}。结合已知信息如下：{kb_content}。{se_content}"

        # 调用模型
        if self.llm == "local":
            paired_history = []
            for i in range(0, len(history), 2):
                pair = history[i:i + 2]
                paired_history.append(pair)
            paired_history.append([query, ''])
            response = model.chat(tokenizer, prompt, paired_history, max_length=self.max_tokens,
                                  top_p=self.top_p, temperature=self.temperature)
        else:
            print(f"chatting with {self.llm}")
            client = OpenAI(api_key=LLM_APIS[self.llm]["key"],
                            base_url=LLM_APIS[self.llm]["url"])
            messages = [{'role': 'system', 'content': prompt}]
            for i in range(0, len(history), 2):
                pair = history[i:i + 2]
                messages.append({'role': 'user', 'content': history[i]})
                messages.append({'role': 'assistant', 'content': history[i+1]})
            messages.append({'role': 'user', 'content': query})
            completion = client.chat.completions.create(model=self.llm, messages=messages,
                                                        temperature=self.temperature, top_p=self.top_p,
                                                        max_tokens=self.max_tokens)
            response = completion.choices[0].message.content

        # 内容格式化
        # 不同大模型返回结果中可能存在特殊标记，添加针对deepseek的清理
        response = re.sub(r'<think>.*?</think>\s*', '', response, flags=re.DOTALL)

        print(f"response: {response}")

        return response

    def report(self, history, compare):
        """
        根据数据和问答历史，按照模板生成报告，保存到本地
        :param history: 历史问答记录
        :param compare: 是否进行对比分析
        :return: 报告文件路径
        """

        if self.template:
            prompt = f"已知{self.region}有以下指标项的推演数据：{self.default_data}"
            prompt += f"""参考下边的模板：
        
            {self.template_content}
            
            保持模板的内容不变，将"<>"之间的内容按照其内容要求进行生成，替换掉原来"<>"之间的内容，包括"<>"。
            """
        else:
            compare_prompt = ""
            if self.tuned_data and compare:
                compare_prompt += f"""优化后的推演数据如下：{self.tuned_data}，
                各个指标项的参数默认值是1，各个指标项优化后的参数如下：{self.parameters}，针对存在优化推演数据的指标项，
                结合对参数的优化进行对比分析。"""
            prompt = f"""已知{self.region}有以下指标项的推演数据：{self.default_data}，根据输入完成报告：
            标题，政策模拟推演分析报告。
            背景，写一段文字，内容包括对数据中各个指标项的定义，指标项用于反映什么场景的现状，有什么意义。
            数据分析，用一段文字详细总结一下各个指标项的数据的变化趋势。{compare_prompt}
            总结，提出明确的建议或策略以改进或提升指标的表现。指出具体的行动步骤和预期的效果。
            请确保报告遵循上述结构和格式要求，以保证内容的清晰度和连贯性。
            """

        # 调用模型
        if self.llm == "local":
            paired_history = []
            for i in range(0, len(history), 2):
                pair = history[i:i + 2]
                paired_history.append(pair)
            response = model.chat(tokenizer, prompt, paired_history, max_length=self.max_tokens,
                                  top_p=self.top_p, temperature=self.temperature)
        else:
            client = OpenAI(api_key=LLM_APIS[self.llm]["key"],
                            base_url=LLM_APIS[self.llm]["url"])
            messages = [{'role': 'system', 'content': '你是一个城市仿真推演相关的数据分析专家，精通数据分析以及报告撰写'}]
            for i in range(0, len(history), 2):
                pair = history[i:i + 2]
                messages.append({'role': 'user', 'content': history[i]})
                messages.append({'role': 'assistant', 'content': history[i+1]})
            messages.append({'role': 'user', 'content': prompt})
            completion = client.chat.completions.create(model=self.llm, messages=messages,
                                                        temperature=self.temperature, top_p=self.top_p,
                                                        max_tokens=self.max_tokens)
            response = completion.choices[0].message.content

        current_time = datetime.datetime.now()
        timestamp_str = current_time.strftime('%Y-%m-%d_%H-%M-%S')
        extension = ".md"
        if self.template:
            _, extension = os.path.splitext(self.template)
        filename = timestamp_str+extension
        output_filepath = os.path.join(REPORT_DIR, filename)

        with open(output_filepath, 'w', encoding='utf-8') as file:
            file.write(response)

        return response, output_filepath
