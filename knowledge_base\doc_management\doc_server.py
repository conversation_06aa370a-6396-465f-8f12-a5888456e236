# -*- coding: utf-8 -*-
import os

from flask import Blueprint, current_app, request, redirect, url_for, render_template, send_from_directory, flash

from configs.app_config import KNOWLEDGE_DOC_DIR
from knowledge_base.knowledge_base_manage import folder2db
from utils.db_utils import get_doc_info, remove_doc_info

doc_blueprint = Blueprint('doc_blueprint', __name__)

if not os.path.exists(KNOWLEDGE_DOC_DIR):
    os.makedirs(KNOWLEDGE_DOC_DIR)

ALLOWED_EXTENSIONS = {'txt', 'doc', 'docx', 'md', 'pdf'}
MAX_CONTENT_LENGTH = 200  # 200MB


def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def allowed_size(file):
    file.seek(0, os.SEEK_END)  # Move to the end of the file to get its size
    filesize = file.tell()
    file.seek(0)  # Move back to the start of the file
    max_size = MAX_CONTENT_LENGTH * 1024 * 1024
    return filesize <= max_size


# @doc_blueprint.route('/doc_server', methods=['GET'])
# def index():
#     files = os.listdir(KNOWLEDGE_DOC_DIR)
#     print(files)
#     current_app.logger.info(f"request: /doc_server")
#     return render_template('index.html', files=files)


@doc_blueprint.route('/doc_server', methods=['GET'])
def index():
    files = os.listdir(KNOWLEDGE_DOC_DIR)
    file_info = []
    db_docs = get_doc_info()
    for file in files:
        if file in db_docs:
            file_info.append({'filename': file, 'mod_time': db_docs[file]})
        else:
            file_info.append({'filename': file, 'mod_time': ''})
    current_app.logger.info(f"request: /doc_server")
    return render_template('index.html', file_info=file_info)


@doc_blueprint.route('/init_kb', methods=['GET'])
def initialize_kb():
    folder2db(mode="recreate_vs")
    flash("知识库初始化完成")
    return redirect(url_for('doc_blueprint.index'))


@doc_blueprint.route('/update_kb', methods=['GET'])
def update_kb():
    folder2db(mode="increment")
    flash("知识库更新完成")
    return redirect(url_for('doc_blueprint.index'))


@doc_blueprint.route('/upload', methods=['POST'])
def upload_files():
    if 'files' not in request.files:
        flash('未接收到文件')
        return redirect(url_for('doc_blueprint.index'))

    files = request.files.getlist('files')
    uploaded_files = []

    for file in files:
        if file.filename == '':
            continue
        if file:
            if not allowed_file(file.filename):
                flash(f'文件上传失败：{file.filename}，只支持下列文件格式：txt, doc, docx, md, pdf')
                continue
            if not allowed_size(file):
                flash(f'文件上传失败：{file.filename}，文件大小不能超过{MAX_CONTENT_LENGTH}MB')
                continue
            file_path = os.path.join(KNOWLEDGE_DOC_DIR, file.filename)

            file.save(file_path)
            uploaded_files.append(file.filename)
        else:
            flash(f'不允许的文件类型: {file.filename}')

    if uploaded_files:
        flash(f'文件上传成功: {", ".join(uploaded_files)}')
    return redirect(url_for('doc_blueprint.index'))


# @doc_blueprint.route('/upload', methods=['POST'])
# def upload_file():
#     if 'file' not in request.files:
#         flash('未接收到文件')
#         return redirect(url_for('doc_blueprint.index'))
#     file = request.files['file']
#     if file.filename == '':
#         flash('未选择文件')
#         return redirect(url_for('doc_blueprint.index'))
#     if file and allowed_file(file.filename):
#         filename = file.filename
#         file.save(os.path.join(KNOWLEDGE_DOC_DIR, filename))
#         flash('文件上传成功')
#         return redirect(url_for('doc_blueprint.index'))
#     else:
#         flash('只允许以下类型的文件：txt, doc, docx, md, pdf')
#         return redirect(url_for('doc_blueprint.index'))


@doc_blueprint.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(KNOWLEDGE_DOC_DIR, filename)


@doc_blueprint.route('/delete/<filename>', methods=['POST'])
def delete_file(filename):
    file_path = os.path.join(KNOWLEDGE_DOC_DIR, filename)
    if os.path.exists(file_path):
        os.remove(file_path)
        remove_doc_info(filename)
        flash('文件删除成功')
    else:
        flash('未找到文件')
    return redirect(url_for('doc_blueprint.index'))


# @doc_blueprint.errorhandler(Exception)
# def handle_exception(e):
#     flash(f"发生了一个错误: {str(e)}")
#     current_app.logger.error(f"发生了一个错误: {str(e)}")
#     return redirect(url_for('doc_blueprint.index'))